# 大乐透数据分析系统

## 📋 项目概述

基于上下文工程优化的大乐透数据分析系统，采用简化的技术栈和高耦合低内聚的架构设计，提供开奖数据管理、模式分析、结果展示等核心功能。

## 🎯 核心功能

- **数据管理**: 开奖数据的增删改查、批量删除、数据更新
- **数据分析**: 奇偶排布、分区比值、红球间隔、蓝球间距分析
- **数据展示**: 简单的HTML页面展示分析结果
- **系统管理**: 基础的数据库操作和文件管理

## 🏗️ 技术架构

- **技术栈**: Python 3.13 + Flask + SQLAlchemy + SQLite + Jinja2
- **架构模式**: 简单单体应用、高耦合低内聚、功能集中
- **算法统一**: 增量计算和普通计算使用同一套算法
- **简单部署**: 单文件应用设计，便于部署和维护

## 🎲 大乐透业务规则

- **红球范围**: 1-35，每期选5个不重复号码，按升序排列
- **蓝球范围**: 1-12，每期选2个不重复号码，按升序排列
- **奇偶排布**: 基于排序后红球计算，格式如"奇偶奇奇偶"
- **分区比值**: 35个红球分7区(1-5,6-10,11-15,16-20,21-25,26-30,31-35)
- **间隔计算**: 使用号码追踪算法，处理首次出现和边界情况

## 🚀 快速开始

### 环境要求

- Python 3.13
- Miniconda（推荐）

### 安装步骤

1. **创建Python环境**
```bash
# 使用Miniconda创建Python 3.13环境
conda create -n lottery-analysis python=3.13
conda activate lottery-analysis
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **启动应用**
```bash
python app.py
```

4. **访问系统**
```
浏览器打开: http://localhost:5000
```

## 📁 项目结构

```
lottery-analysis-system/
├── app.py                      # Flask应用主文件（高耦合设计，包含所有功能）
├── calculations.py             # 统一计算算法模块
├── requirements.txt            # Python依赖包
├── README.md                   # 项目说明文档
├── lottery.db                  # SQLite数据库文件（运行时生成）
├── templates/                  # Jinja2模板目录
│   ├── base.html              # 基础模板
│   ├── index.html             # 主页模板
│   ├── data_list.html         # 数据列表模板
│   ├── add_data.html          # 添加数据模板
│   └── analysis.html          # 分析结果模板
├── static/                     # 静态文件目录
│   ├── css/
│   │   └── style.css          # 样式文件
│   └── js/
│       └── main.js            # JavaScript文件
├── logs/                       # 日志文件目录
│   ├── app.log                # 应用日志
│   └── error.log              # 错误日志
└── tests/                      # 测试文件目录
    ├── test_calculations.py   # 算法一致性测试
    ├── test_database.py       # 数据库操作测试
    └── test_app.py            # 应用功能测试
```

## 🧪 测试

```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行算法一致性测试
python -m pytest tests/test_calculations.py -v

# 代码质量检查
python -m flake8 app.py calculations.py --max-line-length=100
```

## 🔧 配置

系统支持通过环境变量进行配置：

```bash
# Flask配置
export FLASK_HOST=0.0.0.0
export FLASK_PORT=5000
export FLASK_DEBUG=False

# 数据库配置
export DATABASE_URL=sqlite:///lottery.db

# 安全配置
export SECRET_KEY=your-secret-key-here
```

## 📊 核心算法

### 算法一致性保证

系统确保增量计算和普通计算使用完全相同的算法：

- **奇偶排布计算**: 基于排序后红球的奇偶性分析
- **分区比值计算**: 35个红球分7区，统计每区号码数量
- **历史追踪算法**: 计算上次相同模式出现的日期和间隔
- **数据验证算法**: 统一的数据格式和范围验证

### 使用示例

```python
from calculations import LotteryCalculations

# 计算奇偶排布
red_balls = [1, 8, 15, 22, 29]
pattern = LotteryCalculations.calculate_odd_even_pattern(red_balls)
print(pattern)  # 输出: "奇偶奇偶奇"

# 计算分区比值
ratio = LotteryCalculations.calculate_zone_ratio(red_balls)
print(ratio)  # 输出: "1:1:1:1:1:0:0"
```

## 🎯 开发原则

- **高耦合低内聚**: 功能集中在少数文件中，减少系统复杂度
- **算法统一**: 所有计算逻辑集中在calculations.py中
- **简单直接**: 避免过度抽象，保持代码简单易懂
- **测试驱动**: 重点关注算法一致性和数据完整性测试

## 📝 许可证

本项目基于上下文工程方法论开发，用于展示简化架构的有效性。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

---

**技术栈**: Python 3.13 + Flask + SQLAlchemy + SQLite + Jinja2  
**架构模式**: 简单单体应用、高耦合低内聚、功能集中  
**开发方法**: 上下文工程优化、算法统一、测试驱动

"""
大乐透Flask应用主文件 - 高耦合设计

采用高耦合低内聚的设计，功能集中在主文件中。
包含所有路由、视图函数、数据库操作和核心业务逻辑。

这个设计展示了：
1. 简单的Flask应用结构
2. 高耦合设计，功能集中
3. 基础的路由和视图函数
4. 统一的计算算法调用
5. 简单的错误处理
"""

import os
import logging
from logging.handlers import RotatingFileHandler
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from sqlalchemy import create_engine, Column, Integer, String, Date, desc
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
from typing import List, Dict, Any, Optional
from calculations import LotteryCalculations

# Flask应用初始化
app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'lottery-analysis-secret-key-2024')

# 数据库配置
DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///lottery.db')
engine = create_engine(DATABASE_URL, echo=False,
                      connect_args={'check_same_thread': False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 数据模型定义（高耦合设计，直接在主文件中）
class LotteryData(Base):
    """大乐透开奖数据模型"""
    __tablename__ = 'lottery_data'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    period = Column(Integer, unique=True, nullable=False)
    date = Column(Date, nullable=False)
    red_ball_1 = Column(Integer, nullable=False)
    red_ball_2 = Column(Integer, nullable=False)
    red_ball_3 = Column(Integer, nullable=False)
    red_ball_4 = Column(Integer, nullable=False)
    red_ball_5 = Column(Integer, nullable=False)
    blue_ball_1 = Column(Integer, nullable=False)
    blue_ball_2 = Column(Integer, nullable=False)
    # 计算字段
    odd_even_pattern = Column(String(10))
    zone_ratio = Column(String(20))
    red_ball_sum = Column(Integer)
    blue_ball_distance = Column(Integer)
    # 历史追踪字段
    last_odd_even_period = Column(Integer)
    last_odd_even_date = Column(Date)
    odd_even_interval = Column(Integer)
    last_zone_ratio_period = Column(Integer)
    last_zone_ratio_date = Column(Date)
    zone_ratio_interval = Column(Integer)

# 创建数据表
Base.metadata.create_all(bind=engine)

# 日志配置
if not os.path.exists('logs'):
    os.makedirs('logs')

file_handler = RotatingFileHandler(
    'logs/app.log',
    maxBytes=10240000,  # 10MB
    backupCount=5
)
file_handler.setFormatter(logging.Formatter(
    '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
))
file_handler.setLevel(logging.INFO)
app.logger.addHandler(file_handler)
app.logger.setLevel(logging.INFO)
app.logger.info('大乐透数据分析系统启动')

# 数据库操作函数（高耦合设计，直接在主文件中）
def get_db_session():
    """获取数据库会话"""
    return SessionLocal()

def insert_lottery_data(period: int, date_str: str, red_balls: List[int],
                        blue_balls: List[int]) -> bool:
    """
    插入开奖数据 - 使用统一算法（包含历史追踪）

    Args:
        period: 期号
        date_str: 开奖日期字符串
        red_balls: 红球列表
        blue_balls: 蓝球列表

    Returns:
        是否插入成功
    """
    try:
        session = get_db_session()

        # 检查期号是否已存在
        existing = session.query(LotteryData).filter(LotteryData.period == period).first()
        if existing:
            session.close()
            return False, "期号已存在"

        # 获取历史数据用于模式追踪
        historical_data = []
        try:
            history_records = session.query(LotteryData).filter(
                LotteryData.period < period
            ).order_by(LotteryData.period.desc()).limit(1000).all()

            for record in history_records:
                historical_data.append({
                    'period': record.period,
                    'date': record.date.strftime('%Y-%m-%d') if record.date else "",
                    'odd_even_pattern': record.odd_even_pattern,
                    'zone_ratio': record.zone_ratio
                })
        except Exception as e:
            app.logger.warning(f"获取历史数据失败: {e}")
            # 如果获取历史数据失败，继续插入但不进行历史追踪

        # 使用统一算法计算所有字段（包含历史追踪）
        calculated_fields = LotteryCalculations.calculate_all_fields(
            red_balls, blue_balls, period, historical_data
        )

        # 创建新记录
        lottery_data = LotteryData(
            period=period,
            date=datetime.strptime(date_str, '%Y-%m-%d').date(),
            red_ball_1=red_balls[0],
            red_ball_2=red_balls[1],
            red_ball_3=red_balls[2],
            red_ball_4=red_balls[3],
            red_ball_5=red_balls[4],
            blue_ball_1=blue_balls[0],
            blue_ball_2=blue_balls[1],
            odd_even_pattern=calculated_fields['odd_even_pattern'],
            zone_ratio=calculated_fields['zone_ratio'],
            red_ball_sum=calculated_fields['red_ball_sum'],
            blue_ball_distance=calculated_fields['blue_ball_distance'],
            last_odd_even_period=calculated_fields['last_odd_even_period'],
            last_odd_even_date=datetime.strptime(calculated_fields['last_odd_even_date'], '%Y-%m-%d').date()
                if calculated_fields['last_odd_even_date'] else None,
            odd_even_interval=calculated_fields['odd_even_interval'],
            last_zone_ratio_period=calculated_fields['last_zone_ratio_period'],
            last_zone_ratio_date=datetime.strptime(calculated_fields['last_zone_ratio_date'], '%Y-%m-%d').date()
                if calculated_fields['last_zone_ratio_date'] else None,
            zone_ratio_interval=calculated_fields['zone_ratio_interval']
        )

        session.add(lottery_data)
        session.commit()
        session.close()
        
        app.logger.info(f"成功插入期号 {period} 的数据")
        return True, "插入成功"
    except Exception as e:
        app.logger.error(f"插入数据失败: {e}")
        return False, f"插入失败: {str(e)}"

def get_all_lottery_data(limit: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    获取所有开奖数据

    Args:
        limit: 限制返回的记录数

    Returns:
        开奖数据列表
    """
    try:
        session = get_db_session()
        query = session.query(LotteryData).order_by(desc(LotteryData.period))
        
        if limit:
            query = query.limit(limit)
        
        records = query.all()
        
        result = []
        for record in records:
            result.append({
                'id': record.id,
                'period': record.period,
                'date': record.date.strftime('%Y-%m-%d') if record.date else "",
                'red_ball_1': record.red_ball_1,
                'red_ball_2': record.red_ball_2,
                'red_ball_3': record.red_ball_3,
                'red_ball_4': record.red_ball_4,
                'red_ball_5': record.red_ball_5,
                'blue_ball_1': record.blue_ball_1,
                'blue_ball_2': record.blue_ball_2,
                'red_balls': [record.red_ball_1, record.red_ball_2, record.red_ball_3, 
                             record.red_ball_4, record.red_ball_5],
                'blue_balls': [record.blue_ball_1, record.blue_ball_2],
                'odd_even_pattern': record.odd_even_pattern,
                'zone_ratio': record.zone_ratio,
                'red_ball_sum': record.red_ball_sum,
                'blue_ball_distance': record.blue_ball_distance,
                'last_odd_even_period': record.last_odd_even_period,
                'last_odd_even_date': record.last_odd_even_date.strftime('%Y-%m-%d') 
                    if record.last_odd_even_date else None,
                'odd_even_interval': record.odd_even_interval,
                'last_zone_ratio_period': record.last_zone_ratio_period,
                'last_zone_ratio_date': record.last_zone_ratio_date.strftime('%Y-%m-%d') 
                    if record.last_zone_ratio_date else None,
                'zone_ratio_interval': record.zone_ratio_interval
            })
        
        session.close()
        return result
    except Exception as e:
        app.logger.error(f"查询数据失败: {e}")
        return []

def delete_lottery_data(period: int) -> bool:
    """
    删除指定期号的开奖数据

    Args:
        period: 期号

    Returns:
        是否删除成功
    """
    try:
        session = get_db_session()
        record = session.query(LotteryData).filter(LotteryData.period == period).first()

        if record:
            session.delete(record)
            session.commit()
            session.close()
            app.logger.info(f"成功删除期号 {period} 的数据")
            return True
        else:
            session.close()
            return False
    except Exception as e:
        app.logger.error(f"删除数据失败: {e}")
        return False

def recalculate_all_data() -> bool:
    """
    重新计算所有数据 - 使用统一算法（包含历史追踪）

    这个函数用于验证增量计算和普通计算的一致性
    """
    try:
        session = get_db_session()
        # 按期号升序获取所有数据，确保历史追踪的正确性
        all_data = session.query(LotteryData).order_by(LotteryData.period.asc()).all()

        # 用于存储已处理的历史数据
        processed_history = []

        for item in all_data:
            red_balls = [item.red_ball_1, item.red_ball_2, item.red_ball_3,
                        item.red_ball_4, item.red_ball_5]
            blue_balls = [item.blue_ball_1, item.blue_ball_2]

            # 使用统一算法重新计算（包含历史追踪）
            calculated_fields = LotteryCalculations.calculate_all_fields(
                red_balls, blue_balls, item.period, processed_history
            )

            # 更新计算字段
            item.odd_even_pattern = calculated_fields['odd_even_pattern']
            item.zone_ratio = calculated_fields['zone_ratio']
            item.red_ball_sum = calculated_fields['red_ball_sum']
            item.blue_ball_distance = calculated_fields['blue_ball_distance']

            # 更新历史追踪字段
            item.last_odd_even_period = calculated_fields['last_odd_even_period']
            item.last_odd_even_date = datetime.strptime(calculated_fields['last_odd_even_date'], '%Y-%m-%d').date() \
                if calculated_fields['last_odd_even_date'] else None
            item.odd_even_interval = calculated_fields['odd_even_interval']
            item.last_zone_ratio_period = calculated_fields['last_zone_ratio_period']
            item.last_zone_ratio_date = datetime.strptime(calculated_fields['last_zone_ratio_date'], '%Y-%m-%d').date() \
                if calculated_fields['last_zone_ratio_date'] else None
            item.zone_ratio_interval = calculated_fields['zone_ratio_interval']

            # 将当前数据添加到历史记录中，供后续数据使用
            processed_history.append({
                'period': item.period,
                'date': item.date.strftime('%Y-%m-%d') if item.date else "",
                'odd_even_pattern': calculated_fields['odd_even_pattern'],
                'zone_ratio': calculated_fields['zone_ratio']
            })

        session.commit()
        session.close()
        app.logger.info("重新计算所有数据完成")
        return True
    except Exception as e:
        app.logger.error(f"重新计算失败: {e}")
        return False

# Flask路由定义（高耦合设计，所有路由在主文件中）
@app.route('/')
def index():
    """主页"""
    try:
        # 获取基本统计信息
        data = get_all_lottery_data(limit=10)
        total_count = len(get_all_lottery_data())

        stats = {
            'total_count': total_count,
            'recent_data': data[:5] if data else []
        }

        return render_template('index.html', stats=stats)
    except Exception as e:
        app.logger.error(f"主页加载失败: {e}")
        flash('系统错误，请稍后重试')
        return render_template('index.html', stats={'total_count': 0, 'recent_data': []})

@app.route('/data')
def data_list():
    """数据列表页"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # 简单分页实现
        all_data = get_all_lottery_data()
        total = len(all_data)
        start = (page - 1) * per_page
        end = start + per_page
        data = all_data[start:end]

        pagination = {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': (total + per_page - 1) // per_page,
            'has_prev': page > 1,
            'has_next': end < total
        }

        return render_template('data_list.html', data=data, pagination=pagination)
    except Exception as e:
        app.logger.error(f"数据列表加载失败: {e}")
        flash('数据加载失败，请稍后重试')
        return render_template('data_list.html', data=[], pagination={})

@app.route('/add', methods=['GET', 'POST'])
def add_data():
    """添加数据页面"""
    if request.method == 'POST':
        try:
            # 获取表单数据
            period = int(request.form['period'])
            date_str = request.form['date']
            red_balls = [
                int(request.form['red_ball_1']),
                int(request.form['red_ball_2']),
                int(request.form['red_ball_3']),
                int(request.form['red_ball_4']),
                int(request.form['red_ball_5'])
            ]
            blue_balls = [
                int(request.form['blue_ball_1']),
                int(request.form['blue_ball_2'])
            ]

            # 数据验证使用统一验证方法
            is_valid, error_msg = LotteryCalculations.validate_lottery_data(red_balls, blue_balls)
            if not is_valid:
                flash(f'数据验证失败: {error_msg}')
                return render_template('add_data.html')

            # 验证期号
            if period <= 0:
                flash('期号必须大于0')
                return render_template('add_data.html')

            # 验证日期格式
            try:
                datetime.strptime(date_str, '%Y-%m-%d')
            except ValueError:
                flash('日期格式错误，请使用YYYY-MM-DD格式')
                return render_template('add_data.html')

            # 插入数据时使用统一算法
            success, message = insert_lottery_data(period, date_str, red_balls, blue_balls)
            if success:
                flash('数据添加成功')
                return redirect(url_for('data_list'))
            else:
                flash(f'数据添加失败: {message}')
                return render_template('add_data.html')

        except ValueError as e:
            flash('数据格式错误，请检查输入')
            return render_template('add_data.html')
        except Exception as e:
            app.logger.error(f"添加数据失败: {e}")
            flash('系统错误，请稍后重试')
            return render_template('add_data.html')

    return render_template('add_data.html')

@app.route('/delete/<int:period>', methods=['POST'])
def delete_data(period):
    """删除数据"""
    try:
        success = delete_lottery_data(period)
        if success:
            flash(f'成功删除期号 {period} 的数据')
        else:
            flash(f'删除失败，期号 {period} 不存在')
    except Exception as e:
        app.logger.error(f"删除数据失败: {e}")
        flash('删除失败，请稍后重试')

    return redirect(url_for('data_list'))

@app.route('/analysis')
def analysis():
    """分析页面"""
    try:
        data = get_all_lottery_data()

        if not data:
            return render_template('analysis.html', analysis={})

        # 简单的统计分析
        total_count = len(data)
        avg_red_sum = sum(item['red_ball_sum'] for item in data) / total_count

        # 奇偶排布统计
        odd_even_stats = {}
        for item in data:
            pattern = item['odd_even_pattern']
            if pattern:
                odd_even_stats[pattern] = odd_even_stats.get(pattern, 0) + 1

        # 分区比值统计
        zone_ratio_stats = {}
        for item in data:
            ratio = item['zone_ratio']
            if ratio:
                zone_ratio_stats[ratio] = zone_ratio_stats.get(ratio, 0) + 1

        # 红球和值分布
        sum_distribution = {}
        for item in data:
            sum_val = item['red_ball_sum']
            if sum_val:
                sum_range = f"{(sum_val // 10) * 10}-{(sum_val // 10) * 10 + 9}"
                sum_distribution[sum_range] = sum_distribution.get(sum_range, 0) + 1

        analysis_result = {
            'total_count': total_count,
            'avg_red_sum': round(avg_red_sum, 2),
            'odd_even_stats': dict(sorted(odd_even_stats.items(), key=lambda x: x[1], reverse=True)[:10]),
            'zone_ratio_stats': dict(sorted(zone_ratio_stats.items(), key=lambda x: x[1], reverse=True)[:10]),
            'sum_distribution': dict(sorted(sum_distribution.items()))
        }

        return render_template('analysis.html', analysis=analysis_result)
    except Exception as e:
        app.logger.error(f"分析页面加载失败: {e}")
        flash('分析数据加载失败，请稍后重试')
        return render_template('analysis.html', analysis={})

@app.route('/recalculate')
def recalculate():
    """重新计算所有数据"""
    try:
        success = recalculate_all_data()
        if success:
            flash('重新计算完成，所有数据已更新')
        else:
            flash('重新计算失败，请检查日志')
    except Exception as e:
        app.logger.error(f"重新计算失败: {e}")
        flash('重新计算失败，请稍后重试')

    return redirect(url_for('data_list'))

@app.route('/api/stats')
def api_stats():
    """API接口：获取统计信息"""
    try:
        data = get_all_lottery_data()

        if not data:
            return jsonify({'error': 'No data available'})

        stats = {
            'total_count': len(data),
            'latest_period': data[0]['period'] if data else 0,
            'avg_red_sum': round(sum(item['red_ball_sum'] for item in data) / len(data), 2)
        }

        return jsonify(stats)
    except Exception as e:
        app.logger.error(f"API统计信息获取失败: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# 错误处理
@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    app.logger.error(f"内部服务器错误: {error}")
    return render_template('500.html'), 500

if __name__ == '__main__':
    # 从环境变量获取配置，或使用默认值
    host = os.environ.get('FLASK_HOST', '127.0.0.1')
    port = int(os.environ.get('FLASK_PORT', 5000))
    debug = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'

    app.logger.info(f"启动大乐透数据分析系统: {host}:{port}")

    # 启动应用
    app.run(
        host=host,
        port=port,
        debug=debug,
        threaded=True  # 支持多线程
    )

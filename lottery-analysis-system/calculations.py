"""
大乐透计算算法统一模块

这个模块包含所有大乐透相关的计算算法，确保增量计算和普通计算
使用完全相同的算法，保证结果的一致性。

重要原则：
1. 所有计算逻辑集中在这个模块中
2. 增量计算和普通计算必须调用相同的方法
3. 算法实现要简单直接，避免过度抽象
4. 每个方法都要有详细的文档说明
"""

from typing import List, Dict, Any, Tuple, Optional

from datetime import datetime, date


class LotteryCalculations:
    """
    大乐透计算算法统一类
    
    这个类包含所有大乐透相关的计算方法，确保算法的一致性。
    无论是增量计算还是普通计算，都必须使用这个类中的方法。
    """
    
    # 大乐透业务常量
    RED_BALL_MIN = 1
    RED_BALL_MAX = 35
    BLUE_BALL_MIN = 1
    BLUE_BALL_MAX = 12
    RED_BALL_COUNT = 5
    BLUE_BALL_COUNT = 2
    ZONE_COUNT = 7
    BALLS_PER_ZONE = 5
    
    @classmethod
    def calculate_odd_even_pattern(cls, red_balls: List[int]) -> str:
        """
        计算红球奇偶排布 - 统一算法
        
        这是核心算法之一，必须确保增量计算和普通计算
        调用这个相同的方法。
        
        Args:
            red_balls: 红球列表，可以是无序的
            
        Returns:
            奇偶排布字符串，如 "奇偶奇奇偶"
            
        Example:
            >>> calculate_odd_even_pattern([1, 8, 15, 22, 29])
            "奇偶奇偶奇"
        """
        if not red_balls or len(red_balls) != cls.RED_BALL_COUNT:
            return ""
        
        # 验证红球范围
        if not all(cls.RED_BALL_MIN <= ball <= cls.RED_BALL_MAX for ball in red_balls):
            return ""
        
        pattern = ""
        # 必须按升序排列后计算
        for ball in sorted(red_balls):
            pattern += "奇" if ball % 2 == 1 else "偶"
        
        return pattern
    
    @classmethod
    def calculate_zone_ratio(cls, red_balls: List[int]) -> str:
        """
        计算分区比值 - 统一算法
        
        35个红球分7区，每区5个号码：
        第1区: 1-5, 第2区: 6-10, 第3区: 11-15, 第4区: 16-20,
        第5区: 21-25, 第6区: 26-30, 第7区: 31-35
        
        Args:
            red_balls: 红球列表
            
        Returns:
            分区比值字符串，如 "1:1:1:1:1:0:0"
            
        Example:
            >>> calculate_zone_ratio([1, 8, 15, 22, 29])
            "1:1:1:1:1:0:0"
        """
        if not red_balls or len(red_balls) != cls.RED_BALL_COUNT:
            return ""
        
        # 验证红球范围
        if not all(cls.RED_BALL_MIN <= ball <= cls.RED_BALL_MAX for ball in red_balls):
            return ""
        
        zone_counts = [0] * cls.ZONE_COUNT
        
        for ball in red_balls:
            # 计算球所在的区间（0-6）
            zone_index = (ball - 1) // cls.BALLS_PER_ZONE
            if 0 <= zone_index < cls.ZONE_COUNT:
                zone_counts[zone_index] += 1
        
        return ":".join(map(str, zone_counts))
    
    @classmethod
    def calculate_red_ball_sum(cls, red_balls: List[int]) -> int:
        """
        计算红球和值 - 统一算法
        
        Args:
            red_balls: 红球列表
            
        Returns:
            红球和值
            
        Example:
            >>> calculate_red_ball_sum([1, 8, 15, 22, 29])
            75
        """
        if not red_balls or len(red_balls) != cls.RED_BALL_COUNT:
            return 0
        
        # 验证红球范围
        if not all(cls.RED_BALL_MIN <= ball <= cls.RED_BALL_MAX for ball in red_balls):
            return 0
        
        return sum(red_balls)
    
    @classmethod
    def calculate_blue_ball_distance(cls, blue_balls: List[int]) -> int:
        """
        计算蓝球间距 - 统一算法
        
        Args:
            blue_balls: 蓝球列表
            
        Returns:
            蓝球间距（绝对值）
            
        Example:
            >>> calculate_blue_ball_distance([3, 7])
            4
        """
        if not blue_balls or len(blue_balls) != cls.BLUE_BALL_COUNT:
            return 0
        
        # 验证蓝球范围
        if not all(cls.BLUE_BALL_MIN <= ball <= cls.BLUE_BALL_MAX for ball in blue_balls):
            return 0
        
        return abs(blue_balls[1] - blue_balls[0])
    
    @classmethod
    def calculate_historical_tracking(cls, basic_fields: Dict[str, Any], 
                                    current_period: int, 
                                    history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        计算历史追踪字段 - 统一算法
        
        查找上次相同奇偶排布和分区比值的出现日期和间隔。
        
        Args:
            basic_fields: 基础计算字段
            current_period: 当前期号
            history: 历史数据列表
            
        Returns:
            历史追踪字段字典
        """
        tracking_fields = {
            'last_odd_even_period': None,
            'last_odd_even_date': None,
            'odd_even_interval': None,
            'last_zone_ratio_period': None,
            'last_zone_ratio_date': None,
            'zone_ratio_interval': None
        }
        
        if not history:
            return tracking_fields
        
        current_odd_even = basic_fields.get('odd_even_pattern')
        current_zone_ratio = basic_fields.get('zone_ratio')
        
        # 按期号降序查找历史数据
        sorted_history = sorted(history, key=lambda x: x.get('period', 0), reverse=True)
        
        # 查找上次相同奇偶排布
        for record in sorted_history:
            if (record.get('period', 0) < current_period and 
                record.get('odd_even_pattern') == current_odd_even):
                tracking_fields['last_odd_even_period'] = record.get('period')
                tracking_fields['last_odd_even_date'] = record.get('date')
                tracking_fields['odd_even_interval'] = current_period - record.get('period', 0)
                break
        
        # 查找上次相同分区比值
        for record in sorted_history:
            if (record.get('period', 0) < current_period and 
                record.get('zone_ratio') == current_zone_ratio):
                tracking_fields['last_zone_ratio_period'] = record.get('period')
                tracking_fields['last_zone_ratio_date'] = record.get('date')
                tracking_fields['zone_ratio_interval'] = current_period - record.get('period', 0)
                break
        
        return tracking_fields

    @classmethod
    def calculate_all_fields(cls, red_balls: List[int], blue_balls: List[int],
                             period: Optional[int] = None,
                             history: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        计算所有字段 - 统一入口

        确保增量计算和普通计算都调用这个方法，
        保证算法的一致性。

        Args:
            red_balls: 红球列表
            blue_balls: 蓝球列表
            period: 期号（用于历史追踪）
            history: 历史数据（用于历史追踪）

        Returns:
            包含所有计算结果的字典
        """
        # 基础计算字段
        basic_fields = {
            'odd_even_pattern': cls.calculate_odd_even_pattern(red_balls),
            'zone_ratio': cls.calculate_zone_ratio(red_balls),
            'red_ball_sum': cls.calculate_red_ball_sum(red_balls),
            'blue_ball_distance': cls.calculate_blue_ball_distance(blue_balls)
        }

        # 历史追踪字段
        if period is not None and history is not None:
            tracking_fields = cls.calculate_historical_tracking(basic_fields, period, history)
            basic_fields.update(tracking_fields)
        else:
            # 如果没有历史数据，设置默认值
            basic_fields.update({
                'last_odd_even_period': None,
                'last_odd_even_date': None,
                'odd_even_interval': None,
                'last_zone_ratio_period': None,
                'last_zone_ratio_date': None,
                'zone_ratio_interval': None
            })

        return basic_fields

    @classmethod
    def validate_lottery_data(cls, red_balls: List[int], blue_balls: List[int]) -> Tuple[bool, str]:
        """
        验证开奖数据的有效性 - 统一验证算法

        Args:
            red_balls: 红球列表
            blue_balls: 蓝球列表

        Returns:
            (是否有效, 错误信息)

        Example:
            >>> validate_lottery_data([1, 8, 15, 22, 29], [3, 7])
            (True, "")
        """
        # 验证红球
        if not red_balls or len(red_balls) != cls.RED_BALL_COUNT:
            return False, f"红球必须有{cls.RED_BALL_COUNT}个"

        if not all(cls.RED_BALL_MIN <= ball <= cls.RED_BALL_MAX for ball in red_balls):
            return False, f"红球必须在{cls.RED_BALL_MIN}-{cls.RED_BALL_MAX}范围内"

        if len(set(red_balls)) != cls.RED_BALL_COUNT:
            return False, "红球不能重复"

        # 验证蓝球
        if not blue_balls or len(blue_balls) != cls.BLUE_BALL_COUNT:
            return False, f"蓝球必须有{cls.BLUE_BALL_COUNT}个"

        if not all(cls.BLUE_BALL_MIN <= ball <= cls.BLUE_BALL_MAX for ball in blue_balls):
            return False, f"蓝球必须在{cls.BLUE_BALL_MIN}-{cls.BLUE_BALL_MAX}范围内"

        if len(set(blue_balls)) != cls.BLUE_BALL_COUNT:
            return False, "蓝球不能重复"

        return True, ""

    @classmethod
    def batch_calculate(cls, lottery_data_list: List[Dict]) -> List[Dict]:
        """
        批量计算 - 统一批量处理算法

        用于批量处理多条开奖数据，确保每条数据都使用相同的算法。

        Args:
            lottery_data_list: 开奖数据列表，每个元素包含red_balls和blue_balls

        Returns:
            包含计算结果的数据列表
        """
        results = []
        processed_history = []

        # 按期号排序，确保历史追踪的正确性
        sorted_data = sorted(lottery_data_list, key=lambda x: x.get('period', 0))

        for data in sorted_data:
            red_balls = data.get('red_balls', [])
            blue_balls = data.get('blue_balls', [])
            period = data.get('period')

            # 验证数据
            is_valid, error_msg = cls.validate_lottery_data(red_balls, blue_balls)
            if not is_valid:
                print(f"数据验证失败: {error_msg}, 数据: {data}")
                continue

            # 计算所有字段（包含历史追踪）
            calculated_fields = cls.calculate_all_fields(red_balls, blue_balls, period, processed_history)

            # 合并原始数据和计算结果
            result = {**data, **calculated_fields}
            results.append(result)

            # 将当前数据添加到历史记录中，供后续数据使用
            processed_history.append({
                'period': period,
                'date': data.get('date'),
                'odd_even_pattern': calculated_fields['odd_even_pattern'],
                'zone_ratio': calculated_fields['zone_ratio']
            })

        return results


# 便捷函数，保持向后兼容性
def calculate_odd_even_pattern(red_balls: List[int]) -> str:
    """便捷函数：计算奇偶排布"""
    return LotteryCalculations.calculate_odd_even_pattern(red_balls)


def calculate_zone_ratio(red_balls: List[int]) -> str:
    """便捷函数：计算分区比值"""
    return LotteryCalculations.calculate_zone_ratio(red_balls)


def calculate_all_fields(red_balls: List[int], blue_balls: List[int],
                         period: Optional[int] = None,
                         history: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
    """便捷函数：计算所有字段"""
    return LotteryCalculations.calculate_all_fields(red_balls, blue_balls, period, history)


# 使用示例
if __name__ == "__main__":
    # 测试数据
    test_red_balls = [1, 8, 15, 22, 29]
    test_blue_balls = [3, 7]

    print("测试大乐透计算算法:")
    print(f"红球: {test_red_balls}")
    print(f"蓝球: {test_blue_balls}")
    print()

    # 测试所有计算方法
    result = calculate_all_fields(test_red_balls, test_blue_balls)
    for key, value in result.items():
        print(f"{key}: {value}")

    print()
    print("验证数据有效性:")
    is_valid, error_msg = LotteryCalculations.validate_lottery_data(test_red_balls, test_blue_balls)
    print(f"有效性: {is_valid}, 错误信息: {error_msg}")

    print()
    print("测试批量计算:")
    test_data = [
        {
            'period': 24001,
            'date': '2024-01-01',
            'red_balls': [1, 8, 15, 22, 29],
            'blue_balls': [3, 7]
        },
        {
            'period': 24002,
            'date': '2024-01-03',
            'red_balls': [2, 9, 16, 23, 30],
            'blue_balls': [4, 8]
        }
    ]

    batch_results = LotteryCalculations.batch_calculate(test_data)
    for result in batch_results:
        print(f"期号 {result['period']}: 奇偶排布={result['odd_even_pattern']}, 分区比值={result['zone_ratio']}")

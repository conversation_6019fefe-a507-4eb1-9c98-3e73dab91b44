/* 大乐透数据分析系统 - 主样式文件 */

/* 全局样式重置和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    min-height: 100vh;
}

/* 容器和布局 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    position: relative;
}

/* 页面头部 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
    pointer-events: none;
}

.header h1 {
    font-size: 2.5em;
    font-weight: 700;
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
}

.header p {
    font-size: 1.1em;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* 导航菜单 */
.nav {
    background-color: #333;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav ul {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    margin: 0;
    padding: 0;
}

.nav li {
    flex: 1;
    min-width: 120px;
}

.nav a {
    display: block;
    color: white;
    text-decoration: none;
    padding: 15px 20px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.nav a:hover::before {
    left: 100%;
}

.nav a:hover {
    background-color: #555;
    transform: translateY(-2px);
}

.nav a.active {
    background-color: #667eea;
    box-shadow: inset 0 -3px 0 rgba(255,255,255,0.3);
}

/* 主要内容区域 */
.main-content {
    padding: 30px;
    min-height: calc(100vh - 200px);
}

/* Flash消息 */
.flash-messages {
    margin-bottom: 20px;
}

.flash-message {
    padding: 15px 20px;
    margin-bottom: 10px;
    border-radius: 8px;
    border-left: 5px solid;
    position: relative;
    animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.flash-message.success {
    background-color: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.flash-message.error {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.flash-message.info {
    background-color: #d1ecf1;
    border-color: #17a2b8;
    color: #0c5460;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    font-size: 1em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    text-decoration: none;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
    text-decoration: none;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    background-color: #c82333;
    transform: translateY(-2px);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.9em;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table th,
.table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    color: #333;
    text-transform: uppercase;
    font-size: 0.9em;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 球号样式 */
.ball {
    display: inline-block;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
    color: white;
    font-weight: bold;
    font-size: 0.9em;
    margin: 2px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: transform 0.2s ease;
}

.ball:hover {
    transform: scale(1.1);
}

.ball.red {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.ball.blue {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 25px;
    margin-bottom: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 1em;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 页面底部 */
.footer {
    background-color: #333;
    color: white;
    text-align: center;
    padding: 30px 20px;
    margin-top: auto;
}

.footer p {
    margin: 8px 0;
    opacity: 0.8;
    font-size: 0.9em;
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.8em;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 0;
        box-shadow: none;
    }
    
    .header {
        padding: 20px 15px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .nav ul {
        flex-direction: column;
    }
    
    .nav li {
        flex: none;
    }
    
    .main-content {
        padding: 20px 15px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .table {
        font-size: 0.9em;
    }
    
    .ball {
        width: 28px;
        height: 28px;
        line-height: 28px;
        font-size: 0.8em;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.8em;
    }
    
    .main-content {
        padding: 15px 10px;
    }
    
    .card {
        padding: 20px 15px;
    }
    
    .ball {
        width: 24px;
        height: 24px;
        line-height: 24px;
        font-size: 0.75em;
        margin: 1px;
    }
}

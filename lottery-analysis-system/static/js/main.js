/**
 * 大乐透数据分析系统 - 主JavaScript文件
 * 
 * 提供基础的交互功能：
 * - 表单验证
 * - 数据交互
 * - 页面动态效果
 * - 用户体验优化
 */

// 全局配置
const CONFIG = {
    RED_BALL_MIN: 1,
    RED_BALL_MAX: 35,
    BLUE_BALL_MIN: 1,
    BLUE_BALL_MAX: 12,
    RED_BALL_COUNT: 5,
    BLUE_BALL_COUNT: 2,
    ANIMATION_DURATION: 300
};

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * 初始化应用
 */
function initializeApp() {
    // 初始化Flash消息自动隐藏
    initFlashMessages();
    
    // 初始化表单验证
    initFormValidation();
    
    // 初始化数据表格功能
    initDataTables();
    
    // 初始化工具提示
    initTooltips();
    
    // 初始化页面动画
    initAnimations();
    
    // 初始化API状态检查
    initApiStatus();
    
    console.log('大乐透数据分析系统已初始化');
}

/**
 * Flash消息自动隐藏
 */
function initFlashMessages() {
    const flashMessages = document.querySelectorAll('.flash-message');
    
    flashMessages.forEach(message => {
        // 5秒后自动隐藏成功消息
        if (message.classList.contains('success')) {
            setTimeout(() => {
                hideFlashMessage(message);
            }, 5000);
        }
        
        // 添加关闭按钮
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '×';
        closeBtn.className = 'flash-close';
        closeBtn.style.cssText = `
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 1.5em;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.3s;
        `;
        
        closeBtn.addEventListener('click', () => hideFlashMessage(message));
        closeBtn.addEventListener('mouseenter', () => closeBtn.style.opacity = '1');
        closeBtn.addEventListener('mouseleave', () => closeBtn.style.opacity = '0.7');
        
        message.style.position = 'relative';
        message.appendChild(closeBtn);
    });
}

/**
 * 隐藏Flash消息
 */
function hideFlashMessage(message) {
    message.style.transition = 'all 0.3s ease';
    message.style.opacity = '0';
    message.style.transform = 'translateY(-20px)';
    
    setTimeout(() => {
        if (message.parentNode) {
            message.parentNode.removeChild(message);
        }
    }, 300);
}

/**
 * 表单验证初始化
 */
function initFormValidation() {
    // 大乐透号码选择验证
    const redSelects = document.querySelectorAll('.red-select');
    const blueSelects = document.querySelectorAll('.blue-select');
    
    if (redSelects.length > 0) {
        redSelects.forEach(select => {
            select.addEventListener('change', validateLotteryNumbers);
        });
    }
    
    if (blueSelects.length > 0) {
        blueSelects.forEach(select => {
            select.addEventListener('change', validateLotteryNumbers);
        });
    }
    
    // 期号输入验证
    const periodInput = document.getElementById('period');
    if (periodInput) {
        periodInput.addEventListener('input', validatePeriod);
    }
    
    // 表单提交验证
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', handleFormSubmit);
    });
}

/**
 * 验证大乐透号码
 */
function validateLotteryNumbers() {
    const redSelects = document.querySelectorAll('.red-select');
    const blueSelects = document.querySelectorAll('.blue-select');
    
    // 验证红球
    if (redSelects.length > 0) {
        const redValues = Array.from(redSelects)
            .map(select => select.value)
            .filter(value => value !== '');
        
        const redDuplicates = findDuplicates(redValues);
        const redValidation = document.getElementById('redBallValidation');
        
        if (redValidation) {
            if (redDuplicates.length > 0) {
                showValidationError(redValidation, `红球不能重复: ${redDuplicates.join(', ')}`);
            } else if (redValues.length === CONFIG.RED_BALL_COUNT) {
                showValidationSuccess(redValidation, '✅ 红球选择完成');
            } else {
                hideValidation(redValidation);
            }
        }
    }
    
    // 验证蓝球
    if (blueSelects.length > 0) {
        const blueValues = Array.from(blueSelects)
            .map(select => select.value)
            .filter(value => value !== '');
        
        const blueDuplicates = findDuplicates(blueValues);
        const blueValidation = document.getElementById('blueBallValidation');
        
        if (blueValidation) {
            if (blueDuplicates.length > 0) {
                showValidationError(blueValidation, `蓝球不能重复: ${blueDuplicates.join(', ')}`);
            } else if (blueValues.length === CONFIG.BLUE_BALL_COUNT) {
                showValidationSuccess(blueValidation, '✅ 蓝球选择完成');
            } else {
                hideValidation(blueValidation);
            }
        }
    }
}

/**
 * 查找数组中的重复元素
 */
function findDuplicates(array) {
    return array.filter((item, index) => array.indexOf(item) !== index);
}

/**
 * 显示验证错误
 */
function showValidationError(element, message) {
    element.className = 'validation-message error';
    element.textContent = message;
    element.style.display = 'block';
}

/**
 * 显示验证成功
 */
function showValidationSuccess(element, message) {
    element.className = 'validation-message success';
    element.textContent = message;
    element.style.display = 'block';
}

/**
 * 隐藏验证消息
 */
function hideValidation(element) {
    element.style.display = 'none';
}

/**
 * 验证期号
 */
function validatePeriod() {
    const periodInput = document.getElementById('period');
    if (!periodInput) return;
    
    const value = parseInt(periodInput.value);
    const isValid = value > 0 && value <= 99999;
    
    if (periodInput.value && !isValid) {
        periodInput.style.borderColor = '#dc3545';
        showTooltip(periodInput, '期号必须是1-99999之间的正整数');
    } else {
        periodInput.style.borderColor = '#e9ecef';
        hideTooltip(periodInput);
    }
}

/**
 * 处理表单提交
 */
function handleFormSubmit(event) {
    const form = event.target;
    const submitBtn = form.querySelector('button[type="submit"]');
    
    // 显示加载状态
    if (submitBtn) {
        const originalText = submitBtn.textContent;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="loading"></span> 处理中...';
        
        // 如果表单验证失败，恢复按钮状态
        setTimeout(() => {
            if (submitBtn.disabled) {
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            }
        }, 10000); // 10秒超时
    }
}

/**
 * 数据表格功能初始化
 */
function initDataTables() {
    // 表格行点击高亮
    const tableRows = document.querySelectorAll('.data-table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('click', function() {
            // 移除其他行的高亮
            tableRows.forEach(r => r.classList.remove('highlighted'));
            // 添加当前行高亮
            this.classList.add('highlighted');
        });
    });
    
    // 添加表格排序功能
    initTableSorting();
}

/**
 * 表格排序功能
 */
function initTableSorting() {
    const sortableHeaders = document.querySelectorAll('.data-table th[data-sort]');
    
    sortableHeaders.forEach(header => {
        header.style.cursor = 'pointer';
        header.style.userSelect = 'none';
        header.addEventListener('click', () => sortTable(header));
        
        // 添加排序图标
        const icon = document.createElement('span');
        icon.innerHTML = ' ↕️';
        icon.style.fontSize = '0.8em';
        header.appendChild(icon);
    });
}

/**
 * 表格排序
 */
function sortTable(header) {
    const table = header.closest('table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
    const sortType = header.dataset.sort;
    const isAscending = !header.classList.contains('sort-desc');
    
    // 清除其他列的排序状态
    header.parentNode.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    
    // 设置当前列的排序状态
    header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
    
    // 排序行
    rows.sort((a, b) => {
        const aValue = a.children[columnIndex].textContent.trim();
        const bValue = b.children[columnIndex].textContent.trim();
        
        let comparison = 0;
        if (sortType === 'number') {
            comparison = parseFloat(aValue) - parseFloat(bValue);
        } else {
            comparison = aValue.localeCompare(bValue);
        }
        
        return isAscending ? comparison : -comparison;
    });
    
    // 重新插入排序后的行
    rows.forEach(row => tbody.appendChild(row));
}

/**
 * 工具提示初始化
 */
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

/**
 * 显示工具提示
 */
function showTooltip(element, message) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    const tooltip = document.createElement('div');
    tooltip.className = 'custom-tooltip';
    tooltip.textContent = message || element.dataset.tooltip;
    tooltip.style.cssText = `
        position: absolute;
        background: #333;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 0.8em;
        z-index: 1000;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s;
    `;
    
    document.body.appendChild(tooltip);
    
    // 定位工具提示
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
    
    // 显示动画
    setTimeout(() => {
        tooltip.style.opacity = '1';
    }, 10);
    
    element._tooltip = tooltip;
}

/**
 * 隐藏工具提示
 */
function hideTooltip(element) {
    if (element._tooltip) {
        element._tooltip.style.opacity = '0';
        setTimeout(() => {
            if (element._tooltip && element._tooltip.parentNode) {
                element._tooltip.parentNode.removeChild(element._tooltip);
            }
            delete element._tooltip;
        }, 300);
    }
}

/**
 * 页面动画初始化
 */
function initAnimations() {
    // 添加页面加载动画
    const animatedElements = document.querySelectorAll('.card, .summary-card, .feature-card');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, { threshold: 0.1 });
    
    animatedElements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(element);
    });
}

/**
 * API状态检查
 */
function initApiStatus() {
    // 检查API状态
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            console.log('API状态正常:', data);
        })
        .catch(error => {
            console.warn('API连接失败:', error);
        });
}

/**
 * 工具函数：防抖
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 工具函数：节流
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 工具函数：格式化数字
 */
function formatNumber(num, decimals = 2) {
    return parseFloat(num).toFixed(decimals);
}

/**
 * 工具函数：复制到剪贴板
 */
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('已复制到剪贴板', 'success');
        });
    } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('已复制到剪贴板', 'success');
    }
}

/**
 * 显示通知
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 10000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 10);
    
    // 自动隐藏
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// 导出全局函数供模板使用
window.LotteryApp = {
    showNotification,
    copyToClipboard,
    formatNumber,
    validateLotteryNumbers,
    CONFIG
};

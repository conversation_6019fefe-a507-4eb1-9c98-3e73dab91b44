{% extends "base.html" %}

{% block title %}添加数据 - 大乐透数据分析系统{% endblock %}

{% block content %}
<div class="page-header">
    <h2>➕ 添加开奖数据</h2>
    <p>录入新的大乐透开奖数据，系统将自动计算分析字段</p>
</div>

<div class="form-container">
    <form method="POST" class="add-data-form" id="addDataForm">
        <!-- 基本信息 -->
        <div class="form-section">
            <h3>📅 基本信息</h3>
            <div class="form-row">
                <div class="form-group">
                    <label for="period">期号 *</label>
                    <input type="number" id="period" name="period" required 
                           min="1" max="99999" placeholder="例如: 24001">
                    <small class="help-text">请输入期号，必须是正整数</small>
                </div>
                <div class="form-group">
                    <label for="date">开奖日期 *</label>
                    <input type="date" id="date" name="date" required>
                    <small class="help-text">选择开奖日期</small>
                </div>
            </div>
        </div>

        <!-- 红球选择 -->
        <div class="form-section">
            <h3>🔴 红球号码 (1-35，选择5个不重复号码)</h3>
            <div class="balls-input">
                <div class="ball-group">
                    <label>红球 1</label>
                    <select name="red_ball_1" required class="ball-select red-select">
                        <option value="">选择</option>
                        {% for i in range(1, 36) %}
                            <option value="{{ i }}">{{ i }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="ball-group">
                    <label>红球 2</label>
                    <select name="red_ball_2" required class="ball-select red-select">
                        <option value="">选择</option>
                        {% for i in range(1, 36) %}
                            <option value="{{ i }}">{{ i }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="ball-group">
                    <label>红球 3</label>
                    <select name="red_ball_3" required class="ball-select red-select">
                        <option value="">选择</option>
                        {% for i in range(1, 36) %}
                            <option value="{{ i }}">{{ i }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="ball-group">
                    <label>红球 4</label>
                    <select name="red_ball_4" required class="ball-select red-select">
                        <option value="">选择</option>
                        {% for i in range(1, 36) %}
                            <option value="{{ i }}">{{ i }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="ball-group">
                    <label>红球 5</label>
                    <select name="red_ball_5" required class="ball-select red-select">
                        <option value="">选择</option>
                        {% for i in range(1, 36) %}
                            <option value="{{ i }}">{{ i }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="validation-message" id="redBallValidation"></div>
        </div>

        <!-- 蓝球选择 -->
        <div class="form-section">
            <h3>🔵 蓝球号码 (1-12，选择2个不重复号码)</h3>
            <div class="balls-input">
                <div class="ball-group">
                    <label>蓝球 1</label>
                    <select name="blue_ball_1" required class="ball-select blue-select">
                        <option value="">选择</option>
                        {% for i in range(1, 13) %}
                            <option value="{{ i }}">{{ i }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="ball-group">
                    <label>蓝球 2</label>
                    <select name="blue_ball_2" required class="ball-select blue-select">
                        <option value="">选择</option>
                        {% for i in range(1, 13) %}
                            <option value="{{ i }}">{{ i }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="validation-message" id="blueBallValidation"></div>
        </div>

        <!-- 预览区域 -->
        <div class="form-section">
            <h3>👀 数据预览</h3>
            <div class="preview-area">
                <div class="preview-item">
                    <label>期号:</label>
                    <span id="previewPeriod">-</span>
                </div>
                <div class="preview-item">
                    <label>日期:</label>
                    <span id="previewDate">-</span>
                </div>
                <div class="preview-item">
                    <label>红球:</label>
                    <div id="previewRedBalls" class="preview-balls"></div>
                </div>
                <div class="preview-item">
                    <label>蓝球:</label>
                    <div id="previewBlueBalls" class="preview-balls"></div>
                </div>
                <div class="preview-item">
                    <label>奇偶排布:</label>
                    <span id="previewOddEven">-</span>
                </div>
                <div class="preview-item">
                    <label>分区比值:</label>
                    <span id="previewZoneRatio">-</span>
                </div>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div class="form-actions">
            <button type="submit" class="btn btn-primary" id="submitBtn">
                💾 保存数据
            </button>
            <a href="{{ url_for('data_list') }}" class="btn btn-secondary">
                ↩️ 返回列表
            </a>
            <button type="button" class="btn btn-outline" onclick="clearForm()">
                🗑️ 清空表单
            </button>
        </div>
    </form>
</div>

<!-- 使用说明 -->
<div class="help-section">
    <h3>📖 使用说明</h3>
    <div class="help-content">
        <div class="help-item">
            <h4>🎲 大乐透规则</h4>
            <ul>
                <li>红球：从1-35中选择5个不重复号码</li>
                <li>蓝球：从1-12中选择2个不重复号码</li>
                <li>系统会自动计算奇偶排布、分区比值等分析字段</li>
            </ul>
        </div>
        <div class="help-item">
            <h4>🔧 自动计算</h4>
            <ul>
                <li>奇偶排布：基于排序后红球的奇偶性分析</li>
                <li>分区比值：35个红球分7区，统计每区号码数量</li>
                <li>历史追踪：计算上次相同模式出现的日期和间隔</li>
            </ul>
        </div>
        <div class="help-item">
            <h4>✅ 数据验证</h4>
            <ul>
                <li>期号不能重复</li>
                <li>红球和蓝球都不能有重复号码</li>
                <li>所有号码必须在规定范围内</li>
            </ul>
        </div>
    </div>
</div>

<style>
/* 添加数据页面样式 */
.page-header {
    text-align: center;
    margin-bottom: 30px;
}

.form-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.2em;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.form-group input,
.form-group select {
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1em;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.help-text {
    color: #666;
    font-size: 0.9em;
    margin-top: 5px;
}

.balls-input {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.ball-group {
    text-align: center;
}

.ball-group label {
    display: block;
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
}

.ball-select {
    width: 100%;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1em;
    text-align: center;
    transition: all 0.3s;
}

.red-select:focus {
    border-color: #dc3545;
    box-shadow: 0 0 5px rgba(220, 53, 69, 0.3);
}

.blue-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
}

.validation-message {
    margin-top: 10px;
    padding: 10px;
    border-radius: 5px;
    font-size: 0.9em;
    display: none;
}

.validation-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

.validation-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.preview-area {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 2px dashed #ddd;
}

.preview-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.preview-item label {
    font-weight: bold;
    min-width: 80px;
    margin-right: 10px;
}

.preview-balls {
    display: flex;
    gap: 5px;
}

.preview-ball {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50%;
    color: white;
    font-weight: bold;
    font-size: 0.9em;
}

.preview-ball.red {
    background-color: #dc3545;
}

.preview-ball.blue {
    background-color: #007bff;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    font-size: 1em;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover {
    background-color: #5a6fd8;
    text-decoration: none;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    text-decoration: none;
    color: white;
}

.btn-outline {
    background-color: white;
    color: #333;
    border: 2px solid #ddd;
}

.btn-outline:hover {
    background-color: #f8f9fa;
}

.help-section {
    max-width: 800px;
    margin: 40px auto 0;
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.help-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.help-item h4 {
    color: #333;
    margin-bottom: 10px;
}

.help-item ul {
    color: #666;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .form-container,
    .help-section {
        margin: 20px;
        padding: 20px;
    }
    
    .balls-input {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 10px;
    }
    
    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .help-content {
        grid-template-columns: 1fr;
    }
    
    .preview-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .preview-item label {
        min-width: auto;
        margin-right: 0;
    }
}
</style>

<script>
// 表单验证和预览功能
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('addDataForm');
    const redSelects = document.querySelectorAll('.red-select');
    const blueSelects = document.querySelectorAll('.blue-select');
    
    // 监听所有输入变化
    form.addEventListener('change', updatePreview);
    form.addEventListener('input', updatePreview);
    
    // 监听红球选择变化
    redSelects.forEach(select => {
        select.addEventListener('change', validateRedBalls);
    });
    
    // 监听蓝球选择变化
    blueSelects.forEach(select => {
        select.addEventListener('change', validateBlueBalls);
    });
    
    // 设置默认日期为今天
    document.getElementById('date').valueAsDate = new Date();
    
    updatePreview();
});

function validateRedBalls() {
    const redSelects = document.querySelectorAll('.red-select');
    const values = Array.from(redSelects).map(s => s.value).filter(v => v);
    const validation = document.getElementById('redBallValidation');
    
    if (values.length === 0) {
        validation.style.display = 'none';
        return true;
    }
    
    // 检查重复
    const duplicates = values.filter((item, index) => values.indexOf(item) !== index);
    if (duplicates.length > 0) {
        validation.className = 'validation-message error';
        validation.textContent = `红球不能重复，重复的号码: ${duplicates.join(', ')}`;
        return false;
    }
    
    if (values.length === 5) {
        validation.className = 'validation-message success';
        validation.textContent = '✅ 红球选择完成';
    } else {
        validation.style.display = 'none';
    }
    
    return true;
}

function validateBlueBalls() {
    const blueSelects = document.querySelectorAll('.blue-select');
    const values = Array.from(blueSelects).map(s => s.value).filter(v => v);
    const validation = document.getElementById('blueBallValidation');
    
    if (values.length === 0) {
        validation.style.display = 'none';
        return true;
    }
    
    // 检查重复
    const duplicates = values.filter((item, index) => values.indexOf(item) !== index);
    if (duplicates.length > 0) {
        validation.className = 'validation-message error';
        validation.textContent = `蓝球不能重复，重复的号码: ${duplicates.join(', ')}`;
        return false;
    }
    
    if (values.length === 2) {
        validation.className = 'validation-message success';
        validation.textContent = '✅ 蓝球选择完成';
    } else {
        validation.style.display = 'none';
    }
    
    return true;
}

function updatePreview() {
    // 更新期号和日期
    const period = document.getElementById('period').value;
    const date = document.getElementById('date').value;
    
    document.getElementById('previewPeriod').textContent = period || '-';
    document.getElementById('previewDate').textContent = date || '-';
    
    // 更新红球预览
    const redBalls = [];
    document.querySelectorAll('.red-select').forEach(select => {
        if (select.value) {
            redBalls.push(parseInt(select.value));
        }
    });
    
    const redBallsContainer = document.getElementById('previewRedBalls');
    redBallsContainer.innerHTML = '';
    redBalls.sort((a, b) => a - b).forEach(ball => {
        const span = document.createElement('span');
        span.className = 'preview-ball red';
        span.textContent = ball;
        redBallsContainer.appendChild(span);
    });
    
    // 更新蓝球预览
    const blueBalls = [];
    document.querySelectorAll('.blue-select').forEach(select => {
        if (select.value) {
            blueBalls.push(parseInt(select.value));
        }
    });
    
    const blueBallsContainer = document.getElementById('previewBlueBalls');
    blueBallsContainer.innerHTML = '';
    blueBalls.sort((a, b) => a - b).forEach(ball => {
        const span = document.createElement('span');
        span.className = 'preview-ball blue';
        span.textContent = ball;
        blueBallsContainer.appendChild(span);
    });
    
    // 计算奇偶排布
    if (redBalls.length === 5) {
        const sortedRed = [...redBalls].sort((a, b) => a - b);
        const oddEven = sortedRed.map(ball => ball % 2 === 1 ? '奇' : '偶').join('');
        document.getElementById('previewOddEven').textContent = oddEven;
    } else {
        document.getElementById('previewOddEven').textContent = '-';
    }
    
    // 计算分区比值
    if (redBalls.length === 5) {
        const zones = [0, 0, 0, 0, 0, 0, 0];
        redBalls.forEach(ball => {
            const zoneIndex = Math.floor((ball - 1) / 5);
            zones[zoneIndex]++;
        });
        document.getElementById('previewZoneRatio').textContent = zones.join(':');
    } else {
        document.getElementById('previewZoneRatio').textContent = '-';
    }
}

function clearForm() {
    if (confirm('确定要清空所有输入吗？')) {
        document.getElementById('addDataForm').reset();
        document.getElementById('date').valueAsDate = new Date();
        updatePreview();
        document.querySelectorAll('.validation-message').forEach(msg => {
            msg.style.display = 'none';
        });
    }
}

// 表单提交验证
document.getElementById('addDataForm').addEventListener('submit', function(e) {
    if (!validateRedBalls() || !validateBlueBalls()) {
        e.preventDefault();
        alert('请检查红球和蓝球的选择，确保没有重复号码。');
        return false;
    }
    
    const redValues = Array.from(document.querySelectorAll('.red-select')).map(s => s.value).filter(v => v);
    const blueValues = Array.from(document.querySelectorAll('.blue-select')).map(s => s.value).filter(v => v);
    
    if (redValues.length !== 5) {
        e.preventDefault();
        alert('请选择5个红球号码。');
        return false;
    }
    
    if (blueValues.length !== 2) {
        e.preventDefault();
        alert('请选择2个蓝球号码。');
        return false;
    }
    
    // 显示提交状态
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.textContent = '💾 保存中...';
    submitBtn.disabled = true;
});
</script>
{% endblock %}

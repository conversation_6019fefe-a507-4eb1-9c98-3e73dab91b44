{% extends "base.html" %}

{% block title %}数据分析 - 大乐透数据分析系统{% endblock %}

{% block content %}
<div class="page-header">
    <h2>📈 数据分析报告</h2>
    <p>基于历史开奖数据的统计分析，包括奇偶排布、分区比值、和值分布等</p>
</div>

{% if analysis and analysis.total_count > 0 %}
<!-- 总体统计 -->
<div class="summary-section">
    <h3>📊 总体统计</h3>
    <div class="summary-cards">
        <div class="summary-card">
            <div class="card-icon">🎲</div>
            <div class="card-content">
                <div class="card-number">{{ analysis.total_count }}</div>
                <div class="card-label">总开奖期数</div>
            </div>
        </div>
        <div class="summary-card">
            <div class="card-icon">🔢</div>
            <div class="card-content">
                <div class="card-number">{{ analysis.avg_red_sum }}</div>
                <div class="card-label">平均红球和值</div>
            </div>
        </div>
        <div class="summary-card">
            <div class="card-icon">📈</div>
            <div class="card-content">
                <div class="card-number">{{ analysis.odd_even_stats|length }}</div>
                <div class="card-label">奇偶排布类型</div>
            </div>
        </div>
        <div class="summary-card">
            <div class="card-icon">🎯</div>
            <div class="card-content">
                <div class="card-number">{{ analysis.zone_ratio_stats|length }}</div>
                <div class="card-label">分区比值类型</div>
            </div>
        </div>
    </div>
</div>

<!-- 奇偶排布分析 -->
<div class="analysis-section">
    <h3>🔄 奇偶排布分析</h3>
    <p class="section-desc">统计各种奇偶排布模式的出现频率，帮助了解号码的奇偶分布规律。</p>
    
    {% if analysis.odd_even_stats %}
    <div class="chart-container">
        <div class="chart-header">
            <h4>奇偶排布频率分布 (前10名)</h4>
        </div>
        <div class="chart-content">
            <table class="analysis-table">
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>奇偶排布</th>
                        <th>出现次数</th>
                        <th>出现频率</th>
                        <th>频率条</th>
                    </tr>
                </thead>
                <tbody>
                    {% for pattern, count in analysis.odd_even_stats.items() %}
                    <tr>
                        <td class="rank-cell">{{ loop.index }}</td>
                        <td class="pattern-cell">
                            <span class="pattern-display">{{ pattern }}</span>
                        </td>
                        <td class="count-cell">{{ count }}</td>
                        <td class="frequency-cell">
                            {{ "%.1f"|format((count / analysis.total_count * 100)) }}%
                        </td>
                        <td class="bar-cell">
                            <div class="frequency-bar">
                                <div class="bar-fill" style="width: {{ (count / analysis.total_count * 100) }}%"></div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% else %}
    <div class="no-data">暂无奇偶排布数据</div>
    {% endif %}
</div>

<!-- 分区比值分析 -->
<div class="analysis-section">
    <h3>📍 分区比值分析</h3>
    <p class="section-desc">35个红球分为7个区域，统计各种分区比值组合的出现频率。</p>
    
    {% if analysis.zone_ratio_stats %}
    <div class="chart-container">
        <div class="chart-header">
            <h4>分区比值频率分布 (前10名)</h4>
            <div class="zone-legend">
                <span class="legend-item">第1区: 1-5</span>
                <span class="legend-item">第2区: 6-10</span>
                <span class="legend-item">第3区: 11-15</span>
                <span class="legend-item">第4区: 16-20</span>
                <span class="legend-item">第5区: 21-25</span>
                <span class="legend-item">第6区: 26-30</span>
                <span class="legend-item">第7区: 31-35</span>
            </div>
        </div>
        <div class="chart-content">
            <table class="analysis-table">
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>分区比值</th>
                        <th>出现次数</th>
                        <th>出现频率</th>
                        <th>频率条</th>
                    </tr>
                </thead>
                <tbody>
                    {% for ratio, count in analysis.zone_ratio_stats.items() %}
                    <tr>
                        <td class="rank-cell">{{ loop.index }}</td>
                        <td class="ratio-cell">
                            <span class="ratio-display">{{ ratio }}</span>
                        </td>
                        <td class="count-cell">{{ count }}</td>
                        <td class="frequency-cell">
                            {{ "%.1f"|format((count / analysis.total_count * 100)) }}%
                        </td>
                        <td class="bar-cell">
                            <div class="frequency-bar">
                                <div class="bar-fill zone-bar" style="width: {{ (count / analysis.total_count * 100) }}%"></div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% else %}
    <div class="no-data">暂无分区比值数据</div>
    {% endif %}
</div>

<!-- 和值分布分析 -->
<div class="analysis-section">
    <h3>➕ 红球和值分布</h3>
    <p class="section-desc">统计红球和值的分布情况，了解和值的集中区间。</p>
    
    {% if analysis.sum_distribution %}
    <div class="chart-container">
        <div class="chart-header">
            <h4>和值区间分布</h4>
        </div>
        <div class="chart-content">
            <table class="analysis-table">
                <thead>
                    <tr>
                        <th>和值区间</th>
                        <th>出现次数</th>
                        <th>出现频率</th>
                        <th>频率条</th>
                    </tr>
                </thead>
                <tbody>
                    {% for range, count in analysis.sum_distribution.items() %}
                    <tr>
                        <td class="range-cell">{{ range }}</td>
                        <td class="count-cell">{{ count }}</td>
                        <td class="frequency-cell">
                            {{ "%.1f"|format((count / analysis.total_count * 100)) }}%
                        </td>
                        <td class="bar-cell">
                            <div class="frequency-bar">
                                <div class="bar-fill sum-bar" style="width: {{ (count / analysis.total_count * 100) }}%"></div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% else %}
    <div class="no-data">暂无和值分布数据</div>
    {% endif %}
</div>

<!-- 分析说明 -->
<div class="analysis-notes">
    <h3>📝 分析说明</h3>
    <div class="notes-content">
        <div class="note-item">
            <h4>🔄 奇偶排布</h4>
            <p>基于排序后红球的奇偶性分析，格式如"奇偶奇奇偶"。通过统计各种排布的出现频率，可以了解奇偶号码的分布规律。</p>
        </div>
        <div class="note-item">
            <h4>📍 分区比值</h4>
            <p>将35个红球分为7个区域，每区5个号码。分区比值显示每期开奖号码在各区域的分布情况，如"1:1:1:1:1:0:0"表示前5个区域各有1个号码。</p>
        </div>
        <div class="note-item">
            <h4>➕ 和值分布</h4>
            <p>红球和值是5个红球号码的总和，通过统计和值的分布区间，可以了解号码组合的集中趋势。</p>
        </div>
        <div class="note-item">
            <h4>🎯 算法一致性</h4>
            <p>所有分析数据均基于统一的计算算法，确保增量计算和普通计算结果的一致性，保证数据的准确性和可靠性。</p>
        </div>
    </div>
</div>

{% else %}
<!-- 空数据状态 -->
<div class="empty-analysis">
    <div class="empty-icon">📈</div>
    <h3>暂无分析数据</h3>
    <p>系统中还没有足够的开奖数据进行分析，请先添加一些开奖数据。</p>
    <div class="empty-actions">
        <a href="{{ url_for('add_data') }}" class="btn btn-primary">➕ 添加开奖数据</a>
        <a href="{{ url_for('data_list') }}" class="btn btn-secondary">📊 查看数据列表</a>
    </div>
</div>
{% endif %}

<style>
/* 分析页面样式 */
.page-header {
    text-align: center;
    margin-bottom: 40px;
}

.summary-section {
    margin-bottom: 40px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.summary-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    border-left: 4px solid #667eea;
}

.card-icon {
    font-size: 2.5em;
    opacity: 0.8;
}

.card-number {
    font-size: 2em;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.card-label {
    color: #666;
    font-size: 0.9em;
}

.analysis-section {
    background: white;
    margin-bottom: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.analysis-section h3 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: 0;
    padding: 20px 30px;
    font-size: 1.3em;
}

.section-desc {
    padding: 15px 30px 0;
    color: #666;
    margin-bottom: 20px;
}

.chart-container {
    padding: 0 30px 30px;
}

.chart-header {
    margin-bottom: 20px;
}

.chart-header h4 {
    color: #333;
    margin-bottom: 10px;
}

.zone-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 0.9em;
}

.legend-item {
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    color: #666;
}

.analysis-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.analysis-table th,
.analysis-table td {
    padding: 12px;
    text-align: center;
    border-bottom: 1px solid #eee;
}

.analysis-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #333;
}

.analysis-table tbody tr:hover {
    background-color: #f8f9fa;
}

.rank-cell {
    font-weight: bold;
    color: #667eea;
    width: 60px;
}

.pattern-cell,
.ratio-cell {
    width: 150px;
}

.pattern-display {
    font-family: monospace;
    font-weight: bold;
    font-size: 1.1em;
    color: #495057;
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
}

.ratio-display {
    font-family: monospace;
    font-weight: bold;
    color: #495057;
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
}

.count-cell {
    font-weight: bold;
    color: #28a745;
    width: 100px;
}

.frequency-cell {
    font-weight: bold;
    color: #007bff;
    width: 100px;
}

.bar-cell {
    width: 200px;
    padding: 8px 12px;
}

.frequency-bar {
    background-color: #e9ecef;
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.zone-bar {
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
}

.sum-bar {
    background: linear-gradient(90deg, #ffc107 0%, #fd7e14 100%);
}

.range-cell {
    font-weight: bold;
    color: #495057;
    width: 120px;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

.analysis-notes {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-top: 30px;
}

.analysis-notes h3 {
    color: #333;
    margin-bottom: 20px;
}

.notes-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.note-item {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.note-item h4 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.1em;
}

.note-item p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

.empty-analysis {
    text-align: center;
    padding: 80px 20px;
    color: #666;
}

.empty-icon {
    font-size: 5em;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-analysis h3 {
    margin-bottom: 15px;
    color: #333;
}

.empty-analysis p {
    margin-bottom: 30px;
    font-size: 1.1em;
}

.empty-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover {
    background-color: #5a6fd8;
    text-decoration: none;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    text-decoration: none;
    color: white;
}

@media (max-width: 768px) {
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .summary-card {
        flex-direction: column;
        text-align: center;
    }
    
    .chart-container {
        padding: 0 15px 20px;
        overflow-x: auto;
    }
    
    .analysis-table {
        min-width: 600px;
    }
    
    .zone-legend {
        justify-content: center;
    }
    
    .notes-content {
        grid-template-columns: 1fr;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .analysis-section h3 {
        padding: 15px 20px;
        font-size: 1.1em;
    }
    
    .section-desc {
        padding: 10px 20px 0;
    }
}
</style>
{% endblock %}

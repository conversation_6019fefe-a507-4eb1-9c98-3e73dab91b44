<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}大乐透数据分析系统{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        /* 内联基础样式，确保在没有外部CSS时也能正常显示 */
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            min-height: 100vh;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .nav {
            background-color: #333;
            padding: 0;
        }
        
        .nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
        }
        
        .nav li {
            flex: 1;
            min-width: 120px;
        }
        
        .nav a {
            display: block;
            color: white;
            text-decoration: none;
            padding: 15px 20px;
            text-align: center;
            transition: background-color 0.3s;
        }
        
        .nav a:hover {
            background-color: #555;
        }
        
        .nav a.active {
            background-color: #667eea;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .flash-messages {
            margin-bottom: 20px;
        }
        
        .flash-message {
            padding: 12px 20px;
            margin-bottom: 10px;
            border-radius: 4px;
            border-left: 4px solid;
        }
        
        .flash-message.success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .flash-message.error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .flash-message.info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 50px;
        }
        
        .footer p {
            margin: 5px 0;
            opacity: 0.8;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                margin: 0;
                box-shadow: none;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .nav ul {
                flex-direction: column;
            }
            
            .nav li {
                flex: none;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
    {% block extra_head %}{% endblock %}
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>🎱 大乐透数据分析系统</h1>
            <p>基于Python 3.13 + Flask的简单数据分析平台</p>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="nav">
            <ul>
                <li><a href="{{ url_for('index') }}" {% if request.endpoint == 'index' %}class="active"{% endif %}>🏠 首页</a></li>
                <li><a href="{{ url_for('data_list') }}" {% if request.endpoint == 'data_list' %}class="active"{% endif %}>📊 数据管理</a></li>
                <li><a href="{{ url_for('add_data') }}" {% if request.endpoint == 'add_data' %}class="active"{% endif %}>➕ 添加数据</a></li>
                <li><a href="{{ url_for('analysis') }}" {% if request.endpoint == 'analysis' %}class="active"{% endif %}>📈 数据分析</a></li>
                <li><a href="{{ url_for('recalculate') }}" onclick="return confirm('确定要重新计算所有数据吗？这可能需要一些时间。')">🔄 重新计算</a></li>
            </ul>
        </nav>
        
        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- Flash消息显示 -->
            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    <div class="flash-messages">
                        {% for message in messages %}
                            <div class="flash-message {% if '成功' in message or '完成' in message %}success{% elif '失败' in message or '错误' in message %}error{% else %}info{% endif %}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endwith %}
            
            <!-- 页面内容 -->
            {% block content %}{% endblock %}
        </div>
        
        <!-- 页面底部 -->
        <div class="footer">
            <p>大乐透数据分析系统 | 基于Context Engineering方法论开发</p>
            <p>技术栈: Python 3.13 + Flask + SQLAlchemy + SQLite + Jinja2</p>
            <p>架构模式: 简单单体应用、高耦合低内聚、功能集中</p>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_scripts %}{% endblock %}
</body>
</html>

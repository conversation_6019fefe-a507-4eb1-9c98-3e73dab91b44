{% extends "base.html" %}

{% block title %}数据管理 - 大乐透数据分析系统{% endblock %}

{% block content %}
<div class="page-header">
    <h2>📊 开奖数据管理</h2>
    <p>查看和管理所有大乐透开奖数据，支持数据的查看、删除等操作</p>
</div>

<!-- 操作工具栏 -->
<div class="toolbar">
    <div class="toolbar-left">
        <a href="{{ url_for('add_data') }}" class="btn btn-primary">➕ 添加新数据</a>
        <a href="{{ url_for('recalculate') }}" class="btn btn-secondary" 
           onclick="return confirm('确定要重新计算所有数据吗？这可能需要一些时间。')">🔄 重新计算</a>
    </div>
    <div class="toolbar-right">
        {% if data %}
            <span class="data-count">共 {{ pagination.total or data|length }} 条记录</span>
        {% endif %}
    </div>
</div>

<!-- 数据表格 -->
{% if data %}
<div class="data-table-container">
    <table class="data-table">
        <thead>
            <tr>
                <th>期号</th>
                <th>开奖日期</th>
                <th>红球</th>
                <th>蓝球</th>
                <th>奇偶排布</th>
                <th>分区比值</th>
                <th>红球和值</th>
                <th>蓝球间距</th>
                <th>历史追踪</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for item in data %}
            <tr>
                <td class="period-cell">
                    <strong>{{ item.period }}</strong>
                </td>
                <td>{{ item.date }}</td>
                <td>
                    <div class="balls red-balls">
                        {% for ball in item.red_balls %}
                            <span class="ball red">{{ ball }}</span>
                        {% endfor %}
                    </div>
                </td>
                <td>
                    <div class="balls blue-balls">
                        {% for ball in item.blue_balls %}
                            <span class="ball blue">{{ ball }}</span>
                        {% endfor %}
                    </div>
                </td>
                <td>
                    <span class="pattern">{{ item.odd_even_pattern or '-' }}</span>
                </td>
                <td>
                    <span class="ratio">{{ item.zone_ratio or '-' }}</span>
                </td>
                <td class="sum-cell">{{ item.red_ball_sum or '-' }}</td>
                <td class="distance-cell">{{ item.blue_ball_distance or '-' }}</td>
                <td class="tracking-cell">
                    {% if item.odd_even_interval %}
                        <div class="tracking-info">
                            <small>奇偶: {{ item.odd_even_interval }}期前</small>
                        </div>
                    {% endif %}
                    {% if item.zone_ratio_interval %}
                        <div class="tracking-info">
                            <small>分区: {{ item.zone_ratio_interval }}期前</small>
                        </div>
                    {% endif %}
                    {% if not item.odd_even_interval and not item.zone_ratio_interval %}
                        <span class="no-tracking">首次出现</span>
                    {% endif %}
                </td>
                <td class="action-cell">
                    <form method="POST" action="{{ url_for('delete_data', period=item.period) }}" 
                          style="display: inline;" 
                          onsubmit="return confirm('确定要删除期号 {{ item.period }} 的数据吗？')">
                        <button type="submit" class="btn btn-danger btn-sm">🗑️ 删除</button>
                    </form>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- 分页导航 -->
{% if pagination and pagination.pages > 1 %}
<div class="pagination-container">
    <div class="pagination">
        {% if pagination.has_prev %}
            <a href="{{ url_for('data_list', page=pagination.page-1) }}" class="page-btn">« 上一页</a>
        {% endif %}
        
        {% for page_num in range(1, pagination.pages + 1) %}
            {% if page_num == pagination.page %}
                <span class="page-btn current">{{ page_num }}</span>
            {% elif page_num <= 3 or page_num > pagination.pages - 3 or (page_num >= pagination.page - 2 and page_num <= pagination.page + 2) %}
                <a href="{{ url_for('data_list', page=page_num) }}" class="page-btn">{{ page_num }}</a>
            {% elif page_num == 4 or page_num == pagination.pages - 3 %}
                <span class="page-btn dots">...</span>
            {% endif %}
        {% endfor %}
        
        {% if pagination.has_next %}
            <a href="{{ url_for('data_list', page=pagination.page+1) }}" class="page-btn">下一页 »</a>
        {% endif %}
    </div>
    <div class="pagination-info">
        第 {{ pagination.page }} 页，共 {{ pagination.pages }} 页，总计 {{ pagination.total }} 条记录
    </div>
</div>
{% endif %}

{% else %}
<!-- 空数据状态 -->
<div class="empty-state">
    <div class="empty-icon">📊</div>
    <h3>暂无开奖数据</h3>
    <p>系统中还没有任何开奖数据，请先添加一些数据。</p>
    <a href="{{ url_for('add_data') }}" class="btn btn-primary">➕ 添加第一条数据</a>
</div>
{% endif %}

<style>
/* 数据列表页面样式 */
.page-header {
    margin-bottom: 30px;
    text-align: center;
}

.page-header h2 {
    color: #333;
    margin-bottom: 10px;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.toolbar-left {
    display: flex;
    gap: 10px;
}

.toolbar-right {
    color: #666;
    font-size: 0.9em;
}

.data-table-container {
    overflow-x: auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 1000px;
}

.data-table th,
.data-table td {
    padding: 12px 8px;
    text-align: center;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #333;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

.period-cell {
    font-size: 1.1em;
    color: #667eea;
}

.balls {
    display: flex;
    justify-content: center;
    gap: 3px;
    flex-wrap: wrap;
}

.ball {
    display: inline-block;
    width: 28px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border-radius: 50%;
    color: white;
    font-weight: bold;
    font-size: 0.85em;
}

.ball.red {
    background-color: #dc3545;
}

.ball.blue {
    background-color: #007bff;
}

.pattern {
    font-family: monospace;
    font-weight: bold;
    color: #495057;
}

.ratio {
    font-family: monospace;
    font-size: 0.9em;
    color: #495057;
}

.sum-cell,
.distance-cell {
    font-weight: bold;
    color: #28a745;
}

.tracking-cell {
    font-size: 0.8em;
    max-width: 120px;
}

.tracking-info {
    margin: 2px 0;
    color: #666;
}

.no-tracking {
    color: #999;
    font-style: italic;
}

.action-cell {
    white-space: nowrap;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.9em;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover {
    background-color: #5a6fd8;
    text-decoration: none;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    text-decoration: none;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8em;
}

.pagination-container {
    margin-top: 30px;
    text-align: center;
}

.pagination {
    display: inline-flex;
    gap: 5px;
    margin-bottom: 10px;
}

.page-btn {
    display: inline-block;
    padding: 8px 12px;
    border: 1px solid #ddd;
    background-color: white;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s;
}

.page-btn:hover {
    background-color: #f8f9fa;
    text-decoration: none;
    color: #333;
}

.page-btn.current {
    background-color: #667eea;
    color: white;
    border-color: #667eea;
}

.page-btn.dots {
    border: none;
    background: none;
    cursor: default;
}

.pagination-info {
    color: #666;
    font-size: 0.9em;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 20px;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #333;
}

.empty-state p {
    margin-bottom: 30px;
}

@media (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .toolbar-left {
        justify-content: center;
    }
    
    .toolbar-right {
        text-align: center;
    }
    
    .data-table th,
    .data-table td {
        padding: 8px 4px;
        font-size: 0.9em;
    }
    
    .balls {
        gap: 2px;
    }
    
    .ball {
        width: 24px;
        height: 24px;
        line-height: 24px;
        font-size: 0.8em;
    }
    
    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }
}
</style>
{% endblock %}

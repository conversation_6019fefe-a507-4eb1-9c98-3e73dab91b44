{% extends "base.html" %}

{% block title %}首页 - 大乐透数据分析系统{% endblock %}

{% block content %}
<div class="welcome-section">
    <h2>🎯 欢迎使用大乐透数据分析系统</h2>
    <p>这是一个基于上下文工程优化的大乐透数据分析平台，采用简化的技术栈和高耦合低内聚的架构设计。</p>
</div>

<!-- 系统统计信息 -->
<div class="stats-section">
    <h3>📊 系统统计</h3>
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_count or 0 }}</div>
            <div class="stat-label">总开奖期数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ stats.recent_data|length or 0 }}</div>
            <div class="stat-label">最近记录</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">100%</div>
            <div class="stat-label">算法一致性</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">实时</div>
            <div class="stat-label">数据更新</div>
        </div>
    </div>
</div>

<!-- 最近开奖数据 -->
{% if stats.recent_data %}
<div class="recent-data-section">
    <h3>🎲 最近开奖数据</h3>
    <div class="recent-data-table">
        <table>
            <thead>
                <tr>
                    <th>期号</th>
                    <th>开奖日期</th>
                    <th>红球</th>
                    <th>蓝球</th>
                    <th>奇偶排布</th>
                    <th>分区比值</th>
                </tr>
            </thead>
            <tbody>
                {% for item in stats.recent_data %}
                <tr>
                    <td><strong>{{ item.period }}</strong></td>
                    <td>{{ item.date }}</td>
                    <td>
                        <div class="balls red-balls">
                            {% for ball in item.red_balls %}
                                <span class="ball red">{{ ball }}</span>
                            {% endfor %}
                        </div>
                    </td>
                    <td>
                        <div class="balls blue-balls">
                            {% for ball in item.blue_balls %}
                                <span class="ball blue">{{ ball }}</span>
                            {% endfor %}
                        </div>
                    </td>
                    <td><span class="pattern">{{ item.odd_even_pattern or '-' }}</span></td>
                    <td><span class="ratio">{{ item.zone_ratio or '-' }}</span></td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <div class="view-more">
        <a href="{{ url_for('data_list') }}" class="btn btn-primary">查看全部数据 →</a>
    </div>
</div>
{% endif %}

<!-- 功能特性 -->
<div class="features-section">
    <h3>🚀 系统特性</h3>
    <div class="features-grid">
        <div class="feature-card">
            <div class="feature-icon">🔧</div>
            <h4>算法统一</h4>
            <p>增量计算和普通计算使用完全相同的算法，确保结果一致性</p>
        </div>
        
        <div class="feature-card">
            <div class="feature-icon">🏗️</div>
            <h4>高耦合设计</h4>
            <p>功能集中在少数文件中，降低系统复杂度，便于维护</p>
        </div>
        
        <div class="feature-card">
            <div class="feature-icon">🐍</div>
            <h4>Python 3.13</h4>
            <p>使用最新的Python版本，通过Miniconda管理开发环境</p>
        </div>
        
        <div class="feature-card">
            <div class="feature-icon">📱</div>
            <h4>简单界面</h4>
            <p>基于Jinja2模板的简洁界面，专注于数据展示和操作</p>
        </div>
        
        <div class="feature-card">
            <div class="feature-icon">🎲</div>
            <h4>业务专业</h4>
            <p>深度理解大乐透业务规则，提供专业的数据分析功能</p>
        </div>
        
        <div class="feature-card">
            <div class="feature-icon">⚡</div>
            <h4>轻量部署</h4>
            <p>单文件应用，SQLite数据库，部署简单，运行高效</p>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="quick-actions-section">
    <h3>⚡ 快速操作</h3>
    <div class="quick-actions">
        <a href="{{ url_for('add_data') }}" class="action-btn add">
            <span class="action-icon">➕</span>
            <span class="action-text">添加开奖数据</span>
        </a>
        
        <a href="{{ url_for('data_list') }}" class="action-btn view">
            <span class="action-icon">📊</span>
            <span class="action-text">查看数据列表</span>
        </a>
        
        <a href="{{ url_for('analysis') }}" class="action-btn analyze">
            <span class="action-icon">📈</span>
            <span class="action-text">数据分析报告</span>
        </a>
        
        <a href="{{ url_for('recalculate') }}" class="action-btn recalc" 
           onclick="return confirm('确定要重新计算所有数据吗？这可能需要一些时间。')">
            <span class="action-icon">🔄</span>
            <span class="action-text">重新计算数据</span>
        </a>
    </div>
</div>

<style>
/* 首页专用样式 */
.welcome-section {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 10px;
}

.welcome-section h2 {
    color: #333;
    margin-bottom: 15px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 10px;
}

.stat-label {
    color: #666;
    font-size: 0.9em;
}

.recent-data-table table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}

.recent-data-table th,
.recent-data-table td {
    padding: 12px;
    text-align: center;
    border-bottom: 1px solid #ddd;
}

.recent-data-table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.balls {
    display: flex;
    justify-content: center;
    gap: 5px;
}

.ball {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50%;
    color: white;
    font-weight: bold;
    font-size: 0.9em;
}

.ball.red {
    background-color: #dc3545;
}

.ball.blue {
    background-color: #007bff;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.feature-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 3em;
    margin-bottom: 15px;
}

.feature-card h4 {
    color: #333;
    margin-bottom: 10px;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 25px;
    background: white;
    border-radius: 10px;
    text-decoration: none;
    color: #333;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    text-decoration: none;
    color: #333;
}

.action-icon {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.action-text {
    font-weight: bold;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #667eea;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #5a6fd8;
    text-decoration: none;
    color: white;
}

.view-more {
    text-align: center;
    margin-top: 20px;
}

@media (max-width: 768px) {
    .stats-grid,
    .features-grid,
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .balls {
        flex-wrap: wrap;
    }
    
    .recent-data-table {
        overflow-x: auto;
    }
}
</style>
{% endblock %}

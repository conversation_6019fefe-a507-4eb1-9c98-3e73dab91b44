"""
Flask应用功能测试

测试Flask应用的路由、视图函数、模板渲染等功能。
"""

import pytest
import sys
import os
import tempfile
from unittest.mock import patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置测试环境
os.environ['DATABASE_URL'] = 'sqlite:///:memory:'
os.environ['FLASK_DEBUG'] = 'False'

from app import app, Base, engine


class TestFlaskApp:
    """Flask应用测试类"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        app.config['TESTING'] = True
        app.config['WTF_CSRF_ENABLED'] = False  # 禁用CSRF保护以便测试
        
        with app.test_client() as client:
            with app.app_context():
                # 创建数据库表
                Base.metadata.create_all(bind=engine)
                yield client
                # 清理数据库
                Base.metadata.drop_all(bind=engine)
    
    def test_index_page(self, client):
        """测试主页"""
        response = client.get('/')
        assert response.status_code == 200
        assert '大乐透数据分析系统' in response.get_data(as_text=True)
        assert '系统统计' in response.get_data(as_text=True)
    
    def test_data_list_page_empty(self, client):
        """测试空数据列表页"""
        response = client.get('/data')
        assert response.status_code == 200
        assert '数据管理' in response.get_data(as_text=True)
        assert '暂无开奖数据' in response.get_data(as_text=True)
    
    def test_add_data_page_get(self, client):
        """测试添加数据页面GET请求"""
        response = client.get('/add')
        assert response.status_code == 200
        assert '添加开奖数据' in response.get_data(as_text=True)
        assert 'red_ball_1' in response.get_data(as_text=True)
        assert 'blue_ball_1' in response.get_data(as_text=True)
    
    def test_add_data_post_success(self, client):
        """测试成功添加数据"""
        data = {
            'period': '24001',
            'date': '2024-01-01',
            'red_ball_1': '1',
            'red_ball_2': '8',
            'red_ball_3': '15',
            'red_ball_4': '22',
            'red_ball_5': '29',
            'blue_ball_1': '3',
            'blue_ball_2': '7'
        }
        
        response = client.post('/add', data=data, follow_redirects=True)
        assert response.status_code == 200
        
        # 验证重定向到数据列表页
        assert '数据管理' in response.get_data(as_text=True)
        
        # 验证数据已添加
        response = client.get('/data')
        assert '24001' in response.get_data(as_text=True)
    
    def test_add_data_post_validation_error(self, client):
        """测试添加数据验证错误"""
        # 测试红球重复
        data = {
            'period': '24001',
            'date': '2024-01-01',
            'red_ball_1': '1',
            'red_ball_2': '1',  # 重复
            'red_ball_3': '15',
            'red_ball_4': '22',
            'red_ball_5': '29',
            'blue_ball_1': '3',
            'blue_ball_2': '7'
        }
        
        response = client.post('/add', data=data)
        assert response.status_code == 200
        assert '数据验证失败' in response.get_data(as_text=True)
    
    def test_add_data_post_invalid_range(self, client):
        """测试添加数据范围错误"""
        # 测试红球超出范围
        data = {
            'period': '24001',
            'date': '2024-01-01',
            'red_ball_1': '0',  # 超出范围
            'red_ball_2': '8',
            'red_ball_3': '15',
            'red_ball_4': '22',
            'red_ball_5': '29',
            'blue_ball_1': '3',
            'blue_ball_2': '7'
        }
        
        response = client.post('/add', data=data)
        assert response.status_code == 200
        assert '数据验证失败' in response.get_data(as_text=True)
    
    def test_add_data_post_duplicate_period(self, client):
        """测试添加重复期号"""
        data = {
            'period': '24001',
            'date': '2024-01-01',
            'red_ball_1': '1',
            'red_ball_2': '8',
            'red_ball_3': '15',
            'red_ball_4': '22',
            'red_ball_5': '29',
            'blue_ball_1': '3',
            'blue_ball_2': '7'
        }
        
        # 第一次添加
        response1 = client.post('/add', data=data, follow_redirects=True)
        assert response1.status_code == 200
        
        # 第二次添加相同期号
        response2 = client.post('/add', data=data)
        assert response2.status_code == 200
        assert '数据添加失败' in response2.get_data(as_text=True)
    
    def test_delete_data(self, client):
        """测试删除数据"""
        # 先添加数据
        data = {
            'period': '24001',
            'date': '2024-01-01',
            'red_ball_1': '1',
            'red_ball_2': '8',
            'red_ball_3': '15',
            'red_ball_4': '22',
            'red_ball_5': '29',
            'blue_ball_1': '3',
            'blue_ball_2': '7'
        }
        
        client.post('/add', data=data, follow_redirects=True)
        
        # 删除数据
        response = client.post('/delete/24001', follow_redirects=True)
        assert response.status_code == 200
        
        # 验证数据已删除
        response = client.get('/data')
        assert '24001' not in response.get_data(as_text=True)
    
    def test_delete_nonexistent_data(self, client):
        """测试删除不存在的数据"""
        response = client.post('/delete/99999', follow_redirects=True)
        assert response.status_code == 200
        assert '删除失败' in response.get_data(as_text=True)
    
    def test_analysis_page_empty(self, client):
        """测试空数据分析页"""
        response = client.get('/analysis')
        assert response.status_code == 200
        assert '数据分析报告' in response.get_data(as_text=True)
        assert '暂无分析数据' in response.get_data(as_text=True)
    
    def test_analysis_page_with_data(self, client):
        """测试有数据的分析页"""
        # 添加测试数据
        test_data = [
            {
                'period': '24001',
                'date': '2024-01-01',
                'red_ball_1': '1', 'red_ball_2': '8', 'red_ball_3': '15',
                'red_ball_4': '22', 'red_ball_5': '29',
                'blue_ball_1': '3', 'blue_ball_2': '7'
            },
            {
                'period': '24002',
                'date': '2024-01-03',
                'red_ball_1': '2', 'red_ball_2': '9', 'red_ball_3': '16',
                'red_ball_4': '23', 'red_ball_5': '30',
                'blue_ball_1': '4', 'blue_ball_2': '8'
            }
        ]
        
        for data in test_data:
            client.post('/add', data=data, follow_redirects=True)
        
        # 访问分析页
        response = client.get('/analysis')
        assert response.status_code == 200
        assert '总体统计' in response.get_data(as_text=True)
        assert '奇偶排布分析' in response.get_data(as_text=True)
        assert '分区比值分析' in response.get_data(as_text=True)
    
    def test_recalculate_route(self, client):
        """测试重新计算路由"""
        # 添加测试数据
        data = {
            'period': '24001',
            'date': '2024-01-01',
            'red_ball_1': '1',
            'red_ball_2': '8',
            'red_ball_3': '15',
            'red_ball_4': '22',
            'red_ball_5': '29',
            'blue_ball_1': '3',
            'blue_ball_2': '7'
        }
        
        client.post('/add', data=data, follow_redirects=True)
        
        # 执行重新计算
        response = client.get('/recalculate', follow_redirects=True)
        assert response.status_code == 200
        assert '重新计算完成' in response.get_data(as_text=True)
    
    def test_api_stats_empty(self, client):
        """测试API统计接口（空数据）"""
        response = client.get('/api/stats')
        assert response.status_code == 200
        
        json_data = response.get_json()
        assert 'error' in json_data
        assert json_data['error'] == 'No data available'
    
    def test_api_stats_with_data(self, client):
        """测试API统计接口（有数据）"""
        # 添加测试数据
        data = {
            'period': '24001',
            'date': '2024-01-01',
            'red_ball_1': '1',
            'red_ball_2': '8',
            'red_ball_3': '15',
            'red_ball_4': '22',
            'red_ball_5': '29',
            'blue_ball_1': '3',
            'blue_ball_2': '7'
        }
        
        client.post('/add', data=data, follow_redirects=True)
        
        # 调用API
        response = client.get('/api/stats')
        assert response.status_code == 200
        
        json_data = response.get_json()
        assert 'total_count' in json_data
        assert 'latest_period' in json_data
        assert 'avg_red_sum' in json_data
        assert json_data['total_count'] == 1
        assert json_data['latest_period'] == 24001
        assert json_data['avg_red_sum'] == 75.0
    
    def test_data_list_pagination(self, client):
        """测试数据列表分页"""
        # 添加多条测试数据
        for i in range(25):  # 超过一页的数据
            data = {
                'period': str(24001 + i),
                'date': '2024-01-01',
                'red_ball_1': str(1 + (i % 5)),
                'red_ball_2': str(6 + (i % 5)),
                'red_ball_3': str(11 + (i % 5)),
                'red_ball_4': str(16 + (i % 5)),
                'red_ball_5': str(21 + (i % 5)),
                'blue_ball_1': str(1 + (i % 6)),
                'blue_ball_2': str(7 + (i % 6))
            }
            client.post('/add', data=data)
        
        # 测试第一页
        response = client.get('/data')
        assert response.status_code == 200
        assert '第 1 页' in response.get_data(as_text=True)
        
        # 测试第二页
        response = client.get('/data?page=2')
        assert response.status_code == 200
        assert '第 2 页' in response.get_data(as_text=True)
    
    def test_error_handlers(self, client):
        """测试错误处理"""
        # 测试404错误
        response = client.get('/nonexistent-page')
        assert response.status_code == 404
    
    def test_form_validation_edge_cases(self, client):
        """测试表单验证边界情况"""
        # 测试无效期号
        data = {
            'period': '-1',  # 负数
            'date': '2024-01-01',
            'red_ball_1': '1',
            'red_ball_2': '8',
            'red_ball_3': '15',
            'red_ball_4': '22',
            'red_ball_5': '29',
            'blue_ball_1': '3',
            'blue_ball_2': '7'
        }
        
        response = client.post('/add', data=data)
        assert response.status_code == 200
        assert '期号必须大于0' in response.get_data(as_text=True)
        
        # 测试无效日期格式
        data['period'] = '24001'
        data['date'] = 'invalid-date'
        
        response = client.post('/add', data=data)
        assert response.status_code == 200
        assert '日期格式错误' in response.get_data(as_text=True)
    
    def test_template_rendering(self, client):
        """测试模板渲染"""
        # 测试主页模板
        response = client.get('/')
        html = response.get_data(as_text=True)
        assert '<title>' in html
        assert '大乐透数据分析系统' in html
        assert 'nav' in html
        assert 'footer' in html
        
        # 测试添加数据页模板
        response = client.get('/add')
        html = response.get_data(as_text=True)
        assert 'form' in html
        assert 'red_ball_1' in html
        assert 'blue_ball_1' in html
    
    def test_static_files(self, client):
        """测试静态文件访问"""
        # 测试CSS文件
        response = client.get('/static/css/style.css')
        assert response.status_code == 200
        assert 'text/css' in response.content_type
        
        # 测试JavaScript文件
        response = client.get('/static/js/main.js')
        assert response.status_code == 200
        assert 'javascript' in response.content_type
    
    def test_flash_messages(self, client):
        """测试Flash消息"""
        # 成功添加数据应该显示成功消息
        data = {
            'period': '24001',
            'date': '2024-01-01',
            'red_ball_1': '1',
            'red_ball_2': '8',
            'red_ball_3': '15',
            'red_ball_4': '22',
            'red_ball_5': '29',
            'blue_ball_1': '3',
            'blue_ball_2': '7'
        }
        
        response = client.post('/add', data=data, follow_redirects=True)
        assert response.status_code == 200
        # Flash消息会在重定向后的页面显示
        
        # 验证错误也会显示Flash消息
        response = client.post('/add', data=data)  # 重复期号
        assert response.status_code == 200
        assert '数据添加失败' in response.get_data(as_text=True)
    
    @patch('app.app.logger')
    def test_logging(self, mock_logger, client):
        """测试日志记录"""
        # 添加数据应该记录日志
        data = {
            'period': '24001',
            'date': '2024-01-01',
            'red_ball_1': '1',
            'red_ball_2': '8',
            'red_ball_3': '15',
            'red_ball_4': '22',
            'red_ball_5': '29',
            'blue_ball_1': '3',
            'blue_ball_2': '7'
        }
        
        client.post('/add', data=data, follow_redirects=True)
        
        # 验证日志被调用（具体的日志内容验证需要更复杂的mock设置）
        assert mock_logger.info.called or mock_logger.error.called


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])

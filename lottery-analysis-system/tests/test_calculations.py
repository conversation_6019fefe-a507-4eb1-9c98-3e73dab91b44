"""
大乐透计算算法一致性测试

重点测试增量计算和普通计算的一致性，确保算法的正确性和可靠性。
这是系统最关键的测试，验证核心业务逻辑的正确性。
"""

import pytest
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from calculations import LotteryCalculations


class TestLotteryCalculations:
    """大乐透计算算法测试类"""
    
    def test_odd_even_pattern_calculation(self):
        """测试奇偶排布计算的正确性"""
        # 测试用例1：奇偶奇偶奇
        red_balls = [1, 8, 15, 22, 29]
        result = LotteryCalculations.calculate_odd_even_pattern(red_balls)
        assert result == "奇偶奇偶奇"
        
        # 测试用例2：全奇数
        red_balls = [1, 3, 5, 7, 9]
        result = LotteryCalculations.calculate_odd_even_pattern(red_balls)
        assert result == "奇奇奇奇奇"
        
        # 测试用例3：全偶数
        red_balls = [2, 4, 6, 8, 10]
        result = LotteryCalculations.calculate_odd_even_pattern(red_balls)
        assert result == "偶偶偶偶偶"
        
        # 测试用例4：无序输入（应该自动排序）
        red_balls = [29, 1, 22, 8, 15]
        result = LotteryCalculations.calculate_odd_even_pattern(red_balls)
        assert result == "奇偶奇偶奇"
        
        # 测试用例5：边界值
        red_balls = [1, 2, 34, 35, 17]
        result = LotteryCalculations.calculate_odd_even_pattern(red_balls)
        assert result == "奇偶奇偶奇"
    
    def test_zone_ratio_calculation(self):
        """测试分区比值计算的正确性"""
        # 测试用例1：每区一个球
        red_balls = [1, 8, 15, 22, 29]  # 第1,2,3,5,6区各一个
        result = LotteryCalculations.calculate_zone_ratio(red_balls)
        assert result == "1:1:1:0:1:1:0"
        
        # 测试用例2：集中在前几区
        red_balls = [1, 2, 6, 7, 11]  # 第1区2个，第2区2个，第3区1个
        result = LotteryCalculations.calculate_zone_ratio(red_balls)
        assert result == "2:2:1:0:0:0:0"
        
        # 测试用例3：集中在后几区
        red_balls = [26, 27, 31, 32, 35]  # 第6区2个，第7区3个
        result = LotteryCalculations.calculate_zone_ratio(red_balls)
        assert result == "0:0:0:0:0:2:3"
        
        # 测试用例4：边界值测试
        red_balls = [5, 10, 15, 20, 25]  # 每区最后一个号码
        result = LotteryCalculations.calculate_zone_ratio(red_balls)
        assert result == "1:1:1:1:1:0:0"

        # 测试用例5：无序输入
        red_balls = [29, 1, 22, 8, 15]
        result = LotteryCalculations.calculate_zone_ratio(red_balls)
        assert result == "1:1:1:0:1:1:0"
    
    def test_red_ball_sum_calculation(self):
        """测试红球和值计算的正确性"""
        # 测试用例1：基本计算
        red_balls = [1, 8, 15, 22, 29]
        result = LotteryCalculations.calculate_red_ball_sum(red_balls)
        assert result == 75
        
        # 测试用例2：最小值
        red_balls = [1, 2, 3, 4, 5]
        result = LotteryCalculations.calculate_red_ball_sum(red_balls)
        assert result == 15
        
        # 测试用例3：最大值
        red_balls = [31, 32, 33, 34, 35]
        result = LotteryCalculations.calculate_red_ball_sum(red_balls)
        assert result == 165
        
        # 测试用例4：无序输入
        red_balls = [29, 1, 22, 8, 15]
        result = LotteryCalculations.calculate_red_ball_sum(red_balls)
        assert result == 75
    
    def test_blue_ball_distance_calculation(self):
        """测试蓝球间距计算的正确性"""
        # 测试用例1：基本计算
        blue_balls = [3, 7]
        result = LotteryCalculations.calculate_blue_ball_distance(blue_balls)
        assert result == 4
        
        # 测试用例2：最小间距
        blue_balls = [1, 2]
        result = LotteryCalculations.calculate_blue_ball_distance(blue_balls)
        assert result == 1
        
        # 测试用例3：最大间距
        blue_balls = [1, 12]
        result = LotteryCalculations.calculate_blue_ball_distance(blue_balls)
        assert result == 11
        
        # 测试用例4：反向输入
        blue_balls = [7, 3]
        result = LotteryCalculations.calculate_blue_ball_distance(blue_balls)
        assert result == 4
        
        # 测试用例5：相同号码（虽然实际不会出现）
        blue_balls = [5, 5]
        result = LotteryCalculations.calculate_blue_ball_distance(blue_balls)
        assert result == 0
    
    def test_data_validation(self):
        """测试数据验证功能"""
        # 测试有效数据
        red_balls = [1, 8, 15, 22, 29]
        blue_balls = [3, 7]
        is_valid, error_msg = LotteryCalculations.validate_lottery_data(red_balls, blue_balls)
        assert is_valid == True
        assert error_msg == ""
        
        # 测试红球数量错误
        red_balls = [1, 8, 15, 22]  # 只有4个
        blue_balls = [3, 7]
        is_valid, error_msg = LotteryCalculations.validate_lottery_data(red_balls, blue_balls)
        assert is_valid == False
        assert "红球必须有5个" in error_msg
        
        # 测试红球范围错误
        red_balls = [0, 8, 15, 22, 29]  # 0超出范围
        blue_balls = [3, 7]
        is_valid, error_msg = LotteryCalculations.validate_lottery_data(red_balls, blue_balls)
        assert is_valid == False
        assert "红球必须在1-35范围内" in error_msg
        
        # 测试红球重复
        red_balls = [1, 8, 8, 22, 29]  # 8重复
        blue_balls = [3, 7]
        is_valid, error_msg = LotteryCalculations.validate_lottery_data(red_balls, blue_balls)
        assert is_valid == False
        assert "红球不能重复" in error_msg
        
        # 测试蓝球数量错误
        red_balls = [1, 8, 15, 22, 29]
        blue_balls = [3]  # 只有1个
        is_valid, error_msg = LotteryCalculations.validate_lottery_data(red_balls, blue_balls)
        assert is_valid == False
        assert "蓝球必须有2个" in error_msg
        
        # 测试蓝球范围错误
        red_balls = [1, 8, 15, 22, 29]
        blue_balls = [3, 13]  # 13超出范围
        is_valid, error_msg = LotteryCalculations.validate_lottery_data(red_balls, blue_balls)
        assert is_valid == False
        assert "蓝球必须在1-12范围内" in error_msg
        
        # 测试蓝球重复
        red_balls = [1, 8, 15, 22, 29]
        blue_balls = [3, 3]  # 3重复
        is_valid, error_msg = LotteryCalculations.validate_lottery_data(red_balls, blue_balls)
        assert is_valid == False
        assert "蓝球不能重复" in error_msg
    
    def test_calculate_all_fields_basic(self):
        """测试计算所有字段的基本功能"""
        red_balls = [1, 8, 15, 22, 29]
        blue_balls = [3, 7]
        
        result = LotteryCalculations.calculate_all_fields(red_balls, blue_balls)
        
        # 验证所有字段都存在
        expected_fields = [
            'odd_even_pattern', 'zone_ratio', 'red_ball_sum', 'blue_ball_distance',
            'last_odd_even_period', 'last_odd_even_date', 'odd_even_interval',
            'last_zone_ratio_period', 'last_zone_ratio_date', 'zone_ratio_interval'
        ]
        
        for field in expected_fields:
            assert field in result
        
        # 验证基本计算结果
        assert result['odd_even_pattern'] == "奇偶奇偶奇"
        assert result['zone_ratio'] == "1:1:1:0:1:1:0"
        assert result['red_ball_sum'] == 75
        assert result['blue_ball_distance'] == 4
        
        # 验证历史追踪字段默认值
        assert result['last_odd_even_period'] is None
        assert result['last_odd_even_date'] is None
        assert result['odd_even_interval'] is None
        assert result['last_zone_ratio_period'] is None
        assert result['last_zone_ratio_date'] is None
        assert result['zone_ratio_interval'] is None
    
    def test_historical_tracking(self):
        """测试历史追踪功能"""
        # 准备历史数据
        history = [
            {
                'period': 24001,
                'date': '2024-01-01',
                'odd_even_pattern': '奇偶奇偶奇',
                'zone_ratio': '1:1:1:0:1:1:0'
            },
            {
                'period': 24002,
                'date': '2024-01-03',
                'odd_even_pattern': '偶偶偶偶偶',
                'zone_ratio': '2:2:1:0:0:0:0'
            }
        ]
        
        # 测试当前数据
        red_balls = [1, 8, 15, 22, 29]  # 奇偶奇偶奇
        blue_balls = [3, 7]
        current_period = 24003
        
        result = LotteryCalculations.calculate_all_fields(
            red_balls, blue_balls, current_period, history
        )
        
        # 验证历史追踪结果
        assert result['last_odd_even_period'] == 24001
        assert result['last_odd_even_date'] == '2024-01-01'
        assert result['odd_even_interval'] == 2  # 24003 - 24001
        
        assert result['last_zone_ratio_period'] == 24001
        assert result['last_zone_ratio_date'] == '2024-01-01'
        assert result['zone_ratio_interval'] == 2  # 24003 - 24001
    
    def test_algorithm_consistency(self):
        """测试算法一致性 - 核心测试"""
        # 准备测试数据
        test_cases = [
            {
                'red_balls': [1, 8, 15, 22, 29],
                'blue_balls': [3, 7],
                'period': 24001
            },
            {
                'red_balls': [2, 9, 16, 23, 30],
                'blue_balls': [4, 8],
                'period': 24002
            },
            {
                'red_balls': [5, 12, 19, 26, 33],
                'blue_balls': [1, 11],
                'period': 24003
            }
        ]
        
        # 方法1：逐个计算（模拟增量计算）
        incremental_results = []
        processed_history = []
        
        for case in test_cases:
            result = LotteryCalculations.calculate_all_fields(
                case['red_balls'], 
                case['blue_balls'], 
                case['period'], 
                processed_history
            )
            incremental_results.append(result)
            
            # 添加到历史记录
            processed_history.append({
                'period': case['period'],
                'date': f"2024-01-{case['period'] - 24000:02d}",
                'odd_even_pattern': result['odd_even_pattern'],
                'zone_ratio': result['zone_ratio']
            })
        
        # 方法2：批量计算（模拟普通计算）
        batch_data = []
        for i, case in enumerate(test_cases):
            batch_data.append({
                'period': case['period'],
                'date': f"2024-01-{case['period'] - 24000:02d}",
                'red_balls': case['red_balls'],
                'blue_balls': case['blue_balls']
            })
        
        batch_results = LotteryCalculations.batch_calculate(batch_data)
        
        # 验证结果一致性
        assert len(incremental_results) == len(batch_results)
        
        for i in range(len(incremental_results)):
            inc_result = incremental_results[i]
            batch_result = batch_results[i]
            
            # 验证基本计算字段
            assert inc_result['odd_even_pattern'] == batch_result['odd_even_pattern']
            assert inc_result['zone_ratio'] == batch_result['zone_ratio']
            assert inc_result['red_ball_sum'] == batch_result['red_ball_sum']
            assert inc_result['blue_ball_distance'] == batch_result['blue_ball_distance']
            
            # 验证历史追踪字段
            assert inc_result['last_odd_even_period'] == batch_result['last_odd_even_period']
            assert inc_result['last_odd_even_date'] == batch_result['last_odd_even_date']
            assert inc_result['odd_even_interval'] == batch_result['odd_even_interval']
            assert inc_result['last_zone_ratio_period'] == batch_result['last_zone_ratio_period']
            assert inc_result['last_zone_ratio_date'] == batch_result['last_zone_ratio_date']
            assert inc_result['zone_ratio_interval'] == batch_result['zone_ratio_interval']
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空历史数据
        red_balls = [1, 8, 15, 22, 29]
        blue_balls = [3, 7]
        result = LotteryCalculations.calculate_all_fields(red_balls, blue_balls, 24001, [])
        
        assert result['last_odd_even_period'] is None
        assert result['odd_even_interval'] is None
        
        # 测试无匹配历史数据
        history = [
            {
                'period': 24001,
                'date': '2024-01-01',
                'odd_even_pattern': '偶偶偶偶偶',  # 不匹配
                'zone_ratio': '2:2:1:0:0:0:0'  # 不匹配
            }
        ]
        
        result = LotteryCalculations.calculate_all_fields(
            red_balls, blue_balls, 24002, history
        )
        
        assert result['last_odd_even_period'] is None
        assert result['last_zone_ratio_period'] is None
    
    def test_performance(self):
        """测试性能 - 确保算法效率"""
        import time
        
        # 生成大量测试数据
        test_data = []
        for i in range(100):
            test_data.append({
                'period': 24000 + i,
                'date': f'2024-01-{i+1:02d}',
                'red_balls': [1 + (i % 5), 6 + (i % 5), 11 + (i % 5), 16 + (i % 5), 21 + (i % 5)],
                'blue_balls': [1 + (i % 6), 7 + (i % 6)]
            })
        
        # 测试批量计算性能
        start_time = time.time()
        results = LotteryCalculations.batch_calculate(test_data)
        end_time = time.time()
        
        # 验证结果
        assert len(results) == 100
        
        # 性能要求：100条数据处理时间应该小于1秒
        processing_time = end_time - start_time
        assert processing_time < 1.0, f"批量计算性能不达标，耗时: {processing_time:.3f}秒"
        
        print(f"批量计算100条数据耗时: {processing_time:.3f}秒")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])

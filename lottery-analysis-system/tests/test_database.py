"""
数据库操作测试

测试数据库的增删改查操作，确保数据的完整性和一致性。
"""

import pytest
import sys
import os
import tempfile
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置测试数据库
os.environ['DATABASE_URL'] = 'sqlite:///:memory:'

from app import (
    LotteryData, Base, engine, SessionLocal,
    insert_lottery_data, get_all_lottery_data, 
    delete_lottery_data, recalculate_all_data
)


class TestDatabase:
    """数据库操作测试类"""
    
    @pytest.fixture(autouse=True)
    def setup_database(self):
        """每个测试前设置数据库"""
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        yield
        # 清理数据库
        Base.metadata.drop_all(bind=engine)
    
    def test_database_connection(self):
        """测试数据库连接"""
        session = SessionLocal()
        try:
            # 尝试查询，验证连接
            result = session.query(LotteryData).count()
            assert result == 0  # 新数据库应该为空
        finally:
            session.close()
    
    def test_insert_lottery_data_success(self):
        """测试成功插入开奖数据"""
        # 测试数据
        period = 24001
        date_str = "2024-01-01"
        red_balls = [1, 8, 15, 22, 29]
        blue_balls = [3, 7]
        
        # 插入数据
        success, message = insert_lottery_data(period, date_str, red_balls, blue_balls)
        
        # 验证插入成功
        assert success == True
        assert "成功" in message
        
        # 验证数据库中的数据
        data = get_all_lottery_data()
        assert len(data) == 1
        
        record = data[0]
        assert record['period'] == period
        assert record['date'] == date_str
        assert record['red_balls'] == red_balls
        assert record['blue_balls'] == blue_balls
        
        # 验证计算字段
        assert record['odd_even_pattern'] == "奇偶奇偶奇"
        assert record['zone_ratio'] == "1:1:1:0:1:1:0"
        assert record['red_ball_sum'] == 75
        assert record['blue_ball_distance'] == 4
    
    def test_insert_duplicate_period(self):
        """测试插入重复期号"""
        period = 24001
        date_str = "2024-01-01"
        red_balls = [1, 8, 15, 22, 29]
        blue_balls = [3, 7]
        
        # 第一次插入
        success1, message1 = insert_lottery_data(period, date_str, red_balls, blue_balls)
        assert success1 == True
        
        # 第二次插入相同期号
        success2, message2 = insert_lottery_data(period, date_str, red_balls, blue_balls)
        assert success2 == False
        assert "已存在" in message2
        
        # 验证只有一条记录
        data = get_all_lottery_data()
        assert len(data) == 1
    
    def test_insert_with_historical_tracking(self):
        """测试带历史追踪的插入"""
        # 插入第一条数据
        success1, _ = insert_lottery_data(24001, "2024-01-01", [1, 8, 15, 22, 29], [3, 7])
        assert success1 == True
        
        # 插入第二条数据（不同模式）
        success2, _ = insert_lottery_data(24002, "2024-01-03", [2, 9, 16, 23, 30], [4, 8])
        assert success2 == True
        
        # 插入第三条数据（与第一条相同模式）
        success3, _ = insert_lottery_data(24003, "2024-01-05", [3, 10, 17, 24, 31], [5, 9])
        assert success3 == True
        
        # 验证历史追踪
        data = get_all_lottery_data()
        assert len(data) == 3
        
        # 找到第三条记录（期号24003）
        record_24003 = next(r for r in data if r['period'] == 24003)
        
        # 验证奇偶排布追踪（应该追踪到24001）
        assert record_24003['last_odd_even_period'] == 24001
        assert record_24003['last_odd_even_date'] == "2024-01-01"
        assert record_24003['odd_even_interval'] == 2
    
    def test_get_all_lottery_data(self):
        """测试获取所有开奖数据"""
        # 插入多条测试数据
        test_data = [
            (24001, "2024-01-01", [1, 8, 15, 22, 29], [3, 7]),
            (24002, "2024-01-03", [2, 9, 16, 23, 30], [4, 8]),
            (24003, "2024-01-05", [3, 10, 17, 24, 31], [5, 9])
        ]
        
        for period, date_str, red_balls, blue_balls in test_data:
            success, _ = insert_lottery_data(period, date_str, red_balls, blue_balls)
            assert success == True
        
        # 获取所有数据
        data = get_all_lottery_data()
        assert len(data) == 3
        
        # 验证数据按期号降序排列
        periods = [record['period'] for record in data]
        assert periods == [24003, 24002, 24001]
        
        # 测试限制返回数量
        limited_data = get_all_lottery_data(limit=2)
        assert len(limited_data) == 2
        assert limited_data[0]['period'] == 24003  # 最新的
        assert limited_data[1]['period'] == 24002
    
    def test_delete_lottery_data(self):
        """测试删除开奖数据"""
        # 插入测试数据
        period = 24001
        success, _ = insert_lottery_data(period, "2024-01-01", [1, 8, 15, 22, 29], [3, 7])
        assert success == True
        
        # 验证数据存在
        data = get_all_lottery_data()
        assert len(data) == 1
        
        # 删除数据
        delete_success = delete_lottery_data(period)
        assert delete_success == True
        
        # 验证数据已删除
        data = get_all_lottery_data()
        assert len(data) == 0
        
        # 尝试删除不存在的数据
        delete_success = delete_lottery_data(99999)
        assert delete_success == False
    
    def test_recalculate_all_data(self):
        """测试重新计算所有数据"""
        # 插入测试数据
        test_data = [
            (24001, "2024-01-01", [1, 8, 15, 22, 29], [3, 7]),
            (24002, "2024-01-03", [2, 9, 16, 23, 30], [4, 8]),
            (24003, "2024-01-05", [1, 8, 15, 22, 29], [3, 7])  # 与第一条相同模式
        ]
        
        for period, date_str, red_balls, blue_balls in test_data:
            success, _ = insert_lottery_data(period, date_str, red_balls, blue_balls)
            assert success == True
        
        # 获取重新计算前的数据
        data_before = get_all_lottery_data()
        
        # 重新计算
        recalc_success = recalculate_all_data()
        assert recalc_success == True
        
        # 获取重新计算后的数据
        data_after = get_all_lottery_data()
        
        # 验证数据数量不变
        assert len(data_before) == len(data_after)
        
        # 验证计算结果一致
        for i in range(len(data_before)):
            before = data_before[i]
            after = data_after[i]
            
            # 基本字段应该相同
            assert before['period'] == after['period']
            assert before['date'] == after['date']
            assert before['red_balls'] == after['red_balls']
            assert before['blue_balls'] == after['blue_balls']
            
            # 计算字段应该相同
            assert before['odd_even_pattern'] == after['odd_even_pattern']
            assert before['zone_ratio'] == after['zone_ratio']
            assert before['red_ball_sum'] == after['red_ball_sum']
            assert before['blue_ball_distance'] == after['blue_ball_distance']
            
            # 历史追踪字段应该相同
            assert before['last_odd_even_period'] == after['last_odd_even_period']
            assert before['odd_even_interval'] == after['odd_even_interval']
    
    def test_data_integrity(self):
        """测试数据完整性"""
        # 插入数据
        period = 24001
        date_str = "2024-01-01"
        red_balls = [1, 8, 15, 22, 29]
        blue_balls = [3, 7]
        
        success, _ = insert_lottery_data(period, date_str, red_balls, blue_balls)
        assert success == True
        
        # 直接查询数据库验证数据完整性
        session = SessionLocal()
        try:
            record = session.query(LotteryData).filter(LotteryData.period == period).first()
            assert record is not None
            
            # 验证所有字段都有值
            assert record.period == period
            assert record.date == datetime.strptime(date_str, '%Y-%m-%d').date()
            assert record.red_ball_1 == red_balls[0]
            assert record.red_ball_2 == red_balls[1]
            assert record.red_ball_3 == red_balls[2]
            assert record.red_ball_4 == red_balls[3]
            assert record.red_ball_5 == red_balls[4]
            assert record.blue_ball_1 == blue_balls[0]
            assert record.blue_ball_2 == blue_balls[1]
            
            # 验证计算字段
            assert record.odd_even_pattern is not None
            assert record.zone_ratio is not None
            assert record.red_ball_sum is not None
            assert record.blue_ball_distance is not None
            
        finally:
            session.close()
    
    def test_date_handling(self):
        """测试日期处理"""
        # 测试不同日期格式
        test_cases = [
            ("2024-01-01", True),
            ("2024-12-31", True),
            ("2024-02-29", True),  # 闰年
        ]
        
        for date_str, should_succeed in test_cases:
            period = 24000 + len(test_cases)
            success, message = insert_lottery_data(
                period, date_str, [1, 8, 15, 22, 29], [3, 7]
            )
            
            if should_succeed:
                assert success == True, f"日期 {date_str} 应该成功，但失败了: {message}"
            else:
                assert success == False, f"日期 {date_str} 应该失败，但成功了"
    
    def test_concurrent_operations(self):
        """测试并发操作（简单模拟）"""
        import threading
        import time
        
        results = []
        
        def insert_data(period):
            try:
                success, message = insert_lottery_data(
                    period, "2024-01-01", [1, 8, 15, 22, 29], [3, 7]
                )
                results.append((period, success, message))
            except Exception as e:
                results.append((period, False, str(e)))
        
        # 创建多个线程同时插入数据
        threads = []
        for i in range(5):
            thread = threading.Thread(target=insert_data, args=(24001 + i,))
            threads.append(thread)
        
        # 启动所有线程
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(results) == 5
        successful_inserts = [r for r in results if r[1] == True]
        assert len(successful_inserts) == 5  # 所有插入都应该成功（不同期号）
        
        # 验证数据库中的数据
        data = get_all_lottery_data()
        assert len(data) == 5
    
    def test_large_dataset(self):
        """测试大数据集处理"""
        # 插入大量数据
        batch_size = 50
        
        for i in range(batch_size):
            period = 24001 + i
            date_str = f"2024-01-{(i % 28) + 1:02d}"
            red_balls = [1 + (i % 5), 6 + (i % 5), 11 + (i % 5), 16 + (i % 5), 21 + (i % 5)]
            blue_balls = [1 + (i % 6), 7 + (i % 6)]
            
            success, _ = insert_lottery_data(period, date_str, red_balls, blue_balls)
            assert success == True
        
        # 验证数据量
        data = get_all_lottery_data()
        assert len(data) == batch_size
        
        # 测试重新计算大数据集
        import time
        start_time = time.time()
        recalc_success = recalculate_all_data()
        end_time = time.time()
        
        assert recalc_success == True
        
        # 性能要求：50条数据重新计算应该在合理时间内完成
        processing_time = end_time - start_time
        assert processing_time < 5.0, f"大数据集重新计算性能不达标，耗时: {processing_time:.3f}秒"
        
        print(f"重新计算{batch_size}条数据耗时: {processing_time:.3f}秒")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
